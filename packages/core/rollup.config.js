import typescript from "@rollup/plugin-typescript";
import dts from "rollup-plugin-dts";
// import resolve from '@rollup/plugin-node-resolve'
import pkg from "./package.json";

export default [
  {
    input: "modules/index.ts",
    output: [
      {
        file: "lib/bundle.cjs.js",
        format: "cjs",
      },
      {
        file: "lib/bundle.esm.js",
        format: "es",
      },
    ],
    plugins: [
      typescript({
        exclude: "node_modules/**",
        typescript: require("typescript"),
      }),
      // resolve(),
    ],
  },
  {
    input: "./typings.d.ts",
    output: [{ file: pkg.types, format: "es" }],
    plugins: [dts()],
  }
]