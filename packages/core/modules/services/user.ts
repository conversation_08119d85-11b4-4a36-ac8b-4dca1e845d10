import Api from './Api';
import type { API } from '../../types/services';

// 获取所有用户
interface GetAllUsers {
  companyName?: string;
  companyId?: string;
  status?: string;
}
export async function getAllUsers(params?: GetAllUsers): Promise<API.User[]> {
  return Api.get('/user/userlist', {
    params,
  });
}

// 创建用户
interface CreateUser {
  companyName?: string;
  userName: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
  troleType: string;
}
export async function createUser(params: CreateUser): Promise<API.User> {
  return Api.post('/user/create', {
    data: {
      ...params,
    },
  });
}

// 更新用户
interface UpdateUser {
  id: string;
  companyName?: string;
  companyId?: string;
  userName: string;
  phoneNumber: string;
  troleType: string;
  status: string;
}
export async function updateUser(params: UpdateUser): Promise<API.User> {
  return Api.post('/user/create', {
    data: {
      ...params,
    },
  });
}

export default {
  getAllUsers,
  createUser,
  updateUser,
};
