{"name": "@xly/user", "version": "0.0.1", "private": true, "author": "intvergil <<EMAIL>>", "scripts": {"dev": "UMI_ENV=dev umi dev", "dev:host": "UMI_ENV=host umi dev", "dev:prod": "UMI_ENV=prod umi dev", "build": "UMI_ENV=prod umi build", "build:dev": "UMI_ENV=dev umi build"}, "dependencies": {"ahooks": "3.7.2", "antd": "5.3.0", "aws-sdk": "^2.1288.0", "umi": "4.0.36"}, "devDependencies": {"@types/react": "18.0.26", "@types/react-dom": "18.0.9", "@umijs/plugins": "^4.0.36", "tailwindcss": "^3"}}