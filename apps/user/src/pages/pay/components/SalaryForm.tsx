import { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Cascader,
  DatePicker,
  Col,
  Row,
  Space,
  Descriptions,
  InputNumber,
} from 'antd';
import dayjs from 'dayjs';
////
import TIMES from '@xly/configs/options/times.json';
import type { SKFormProps } from '@/hooks/useSKForm';
import { useFormBasic } from '@/hooks/useSKForm';
import { formatterEn } from '@/utils/function';

import S3Upload from '@/pages/profile/components/S3Upload';
import { useUserInfo } from '@/hooks/useUserInfo';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

interface DataSource extends API.SalaryRecord {}

export interface SalaryFormProps extends SKFormProps<DataSource> {
  options: {
    SalaryApplyTypes: any[];
    SalaryDepartments: any[];
    userOptions: any[];
  };
}

const SalaryForm: React.FC<SalaryFormProps> = (props) => {
  // state
  const { userInfo } = useUserInfo();
  const { modalProps, formProps } = useFormBasic(props);
  const { SalaryApplyTypes, SalaryDepartments } = props?.options;
  const userOptions = props?.options?.userOptions?.filter(
    ({ role }) => role === 0 || role === 1,
  );

  const fullName =
    (userInfo.last_name_cn || '').trim() +
    (userInfo.first_name_cn || '').trim() +
    userInfo.birthday;
  const fullPath = 'salary_file_links/' + dayjs().format('YYYYMM/') + fullName;

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue({
        // 审核人
        check_user: props?.dataSource?.check_user,

        // 工作日期
        work_date: props?.dataSource?.work_date
          ? dayjs(props?.dataSource?.work_date)
          : null,

        // 部门
        department_ids: props?.dataSource?.department_ids,

        // 申报类型: 1时长 2件数 3字数 4仅交通费 5定给 6其他
        apply_type: props?.dataSource?.apply_type,

        // 单价
        salary_per: props?.dataSource?.salary_per || 1000,

        // 开始时间
        start_time: props?.dataSource?.start_time
          ? dayjs(props?.dataSource?.start_time)
          : null,

        start_time_str: props?.dataSource?.start_time_str,

        // 结束时间
        end_time: props?.dataSource?.end_time
          ? dayjs(props?.dataSource?.end_time)
          : null,

        end_time_str: props?.dataSource?.end_time_str,

        // 人数/件数/字数
        amount: props?.dataSource?.amount,

        // 交通费起点
        travel_start: props?.dataSource?.travel_start,

        // 交通费终点
        travel_end: props?.dataSource?.travel_end,

        // 交通费
        travel_fee: props?.dataSource?.travel_fee,

        // 工作内容
        work_content: props?.dataSource?.work_content,

        // 劳动时间
        work_hours: props?.dataSource?.work_hours || 0,

        // 休息时间
        rest_hours: props?.dataSource?.rest_hours || 0,

        // 此项工作收入额
        final_salary: props?.dataSource?.final_salary || 0,

        // 备注
        file_links: props?.dataSource?.file_links || [],

        // 备注
        memo: props?.dataSource?.memo,
      });
    }
  }, [props]);

  const checkUserId = Form.useWatch('check_user', formProps.form);
  const departments = SalaryDepartments;
  const departmentIds = Form.useWatch('department_ids', formProps.form);
  const d1 = SalaryDepartments?.find(
    ({ value }) => value === departmentIds?.[0],
  );
  const d2 = d1?.children?.find(
    ({ value }: any) => value === departmentIds?.[1],
  );

  const applyTypes = SalaryApplyTypes?.filter(({ value }) =>
    d2?.role?.includes(value),
  );
  const applyTypeId = Form.useWatch('apply_type', formProps.form);
  const applyType = applyTypes?.find(({ value }) => value === applyTypeId);

  const work_hours = Form.useWatch('work_hours', formProps.form);
  const rest_hours = Form.useWatch('rest_hours', formProps.form);
  const final_salary = Form.useWatch('final_salary', formProps.form);

  // action
  const handleSumChange = () => {
    let {
      salary_per = null,
      start_time = null,
      start_time_str = null,
      end_time = null,
      end_time_str = null,
      amount = null,
      travel_fee = null,
    } = formProps.form.getFieldsValue();
    let work_hours = 0;
    let rest_hours = 0;
    let final_salary = 0;
    if (start_time_str) {
      const [h, mm] = start_time_str?.split(':');
      start_time = dayjs()
        ?.set('hour', +h)
        ?.set('minute', +mm)
        ?.set('second', 0);
    }
    if (end_time_str) {
      const [h, mm] = end_time_str?.split(':');
      end_time = dayjs()?.set('hour', +h)?.set('minute', +mm)?.set('second', 0);
    }
    switch (applyTypeId) {
      case 1:
        work_hours =
          end_time
            ?.startOf('minute')
            ?.diff(start_time?.startOf('minute'), 'minute') || 0;
        work_hours = work_hours / 60;
        if (work_hours >= 12) {
          rest_hours = 2;
        } else if (work_hours >= 6) {
          rest_hours = 1;
        }
        final_salary = Math.floor(
          parseFloat(
            ((work_hours - rest_hours) * salary_per + travel_fee).toFixed(12),
          ),
        );
        break;
      case 2:
        final_salary = amount * salary_per + travel_fee;
        break;
      case 3:
        final_salary = amount * salary_per + travel_fee;
        break;
      case 4:
        final_salary = amount * salary_per;
        break;
      case 5:
        final_salary = travel_fee;
        break;
      case 6:
        final_salary = salary_per;
        break;
      case 7:
        work_hours = end_time?.diff(start_time, 'minute') || 0;
        work_hours = work_hours / 60;
        if (work_hours >= 12) {
          rest_hours = 2;
        } else if (work_hours >= 8) {
          rest_hours = 1;
        } else if (work_hours >= 6) {
          rest_hours = 0.75;
        }
        final_salary = salary_per;
        break;
      default:
        break;
    }
    formProps.form.setFieldsValue({ work_hours, rest_hours, final_salary });
  };
  const handleApplyTypeChange = ({ apply_type }: any) => {
    if (apply_type) {
      let resetValues: any = {
        work_hours: 0,
        rest_hours: 0,
        final_salary: 0,
        salary_per: null,
        start_time: null,
        end_time: null,
        start_tim_str: null,
        end_time_str: null,
        amount: null,
        travel_start: null,
        travel_end: null,
        travel_fee: null,
      };
      // if (departmentId === 19 && apply_type === 2) {
      //   resetValues.salary_per = 2000;
      // }
      formProps.form.setFieldsValue(resetValues);
    }
  };

  return (
    <Modal {...modalProps} width={1500}>
      <Form
        name="SalaryForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
        onValuesChange={handleApplyTypeChange}
      >
        <Row className="mt-12">
          <Col span={10}>
            <Form.Item
              label="日期"
              name="work_date"
              rules={[{ required: true }]}
            >
              <DatePicker placeholder="工作日期" />
            </Form.Item>
            <Form.Item
              label="⼯作负责人"
              name="check_user"
              rules={[{ required: true }]}
            >
              <Select options={userOptions} />
            </Form.Item>
            <Form.Item
              label="⼯作所属部⻔"
              name="department_ids"
              rules={[{ required: true }]}
            >
              <Cascader options={departments} disabled={!checkUserId} />
            </Form.Item>
            <Form.Item
              label="计费⽅式"
              name="apply_type"
              rules={[{ required: true }]}
            >
              <Select options={applyTypes} disabled={!departmentIds?.[0]} />
            </Form.Item>
            <Form.Item
              label="⼯作内容"
              name="work_content"
              rules={[{ required: applyTypeId === 7 }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="备注"
              name="memo"
              // rules={[{ required: [8, 15, 19].includes(departmentId) }]}
            >
              <Input.TextArea
              // placeholder={
              //   departmentId === 19 ? '备注学生姓名以及试讲内容】' : ''
              // }
              />
            </Form.Item>
          </Col>
          <Col span={14}>
            <Descriptions className="mb-4" size="small" bordered>
              <Descriptions.Item label="劳动时间">
                <Form.Item name="work_hours" noStyle>
                  <>{work_hours} 小时</>
                </Form.Item>
              </Descriptions.Item>
              <Descriptions.Item label="休息时间">
                <Form.Item name="rest_hours" noStyle>
                  <> {rest_hours} 小时</>
                </Form.Item>
              </Descriptions.Item>
              <Descriptions.Item label="⼯作收⼊">
                <Form.Item name="final_salary" noStyle>
                  <>{formatterEn(final_salary)}</>
                </Form.Item>
              </Descriptions.Item>
            </Descriptions>
            {!!applyType?.rule?.salary_per && (
              <Form.Item
                label="⼯作单价"
                name="salary_per"
                rules={[{ required: applyType?.rule?.salary_per === 1 }]}
              >
                <InputNumber
                  className="w-40"
                  // disabled={departmentId === 19 && applyTypeId === 2}
                  formatter={(value) => (value ? formatterEn(value) : '')}
                  onChange={handleSumChange}
                  placeholder="日元"
                />
              </Form.Item>
            )}
            {!!applyType?.rule?.start_time && (
              <Form.Item label="⼯作时间" required={applyTypeId !== 7}>
                <Space>
                  <Form.Item
                    name="start_time_str"
                    label="开始时间"
                    noStyle
                    rules={[{ required: applyType?.rule?.start_time === 1 }]}
                  >
                    <Select
                      style={{ width: 120 }}
                      options={TIMES}
                      showSearch
                      placeholder="可搜索"
                      onChange={handleSumChange}
                    />
                  </Form.Item>
                  <Form.Item noStyle>~</Form.Item>
                  <Form.Item
                    name="end_time_str"
                    label="结束时间"
                    dependencies={['start_time_str']}
                    noStyle
                    rules={[
                      { required: applyType?.rule?.start_time === 1 },
                      {
                        validator: () => {
                          const start =
                            formProps.form.getFieldValue('start_time_str');
                          const end =
                            formProps.form.getFieldValue('end_time_str');
                          if (start >= end) {
                            return Promise.reject(
                              new Error(
                                '请确认工作时间是否正确；隔天工作请分两条进行申报！',
                              ),
                            );
                          } else {
                            return Promise.resolve();
                          }
                        },
                      },
                    ]}
                  >
                    <Select
                      style={{ width: 120 }}
                      options={TIMES}
                      showSearch
                      placeholder="可搜索"
                      onChange={handleSumChange}
                    />
                  </Form.Item>
                </Space>
              </Form.Item>
            )}
            {!!applyType?.rule?.amount && (
              <Form.Item
                label="⼈数/件数/字数（仅输⼊数字）"
                name="amount"
                rules={[{ required: applyType?.rule?.amount === 1 }]}
              >
                <Input onChange={handleSumChange} />
              </Form.Item>
            )}
            {!!applyType?.rule?.travel_start && (
              <Form.Item
                label="交通费（起始站）"
                name="travel_start"
                rules={[{ required: applyType?.rule?.travel_start === 1 }]}
              >
                <Input />
              </Form.Item>
            )}
            {!!applyType?.rule?.travel_end && (
              <Form.Item
                label="交通费（终点站）"
                name="travel_end"
                rules={[{ required: applyType?.rule?.travel_end === 1 }]}
              >
                <Input />
              </Form.Item>
            )}
            {!!applyType?.rule?.travel_fee && (
              <Form.Item
                label="交通费⽤（往返⾦额）"
                name="travel_fee"
                rules={[{ required: applyType?.rule?.travel_fee === 1 }]}
              >
                <InputNumber
                  className="w-40"
                  formatter={(value) => (value ? formatterEn(value) : '')}
                  onChange={handleSumChange}
                  placeholder="日元"
                />
              </Form.Item>
            )}
          </Col>
          <Col span={24}>
            <Form.Item
              labelCol={{ span: 2 }}
              wrapperCol={{ span: 22 }}
              label="附件"
              name="file_links"
              valuePropName="fileList"
              getValueFromEvent={(e) => {
                if (Array.isArray(e)) {
                  return e;
                }
                return e?.fileList;
              }}
              // rules={[{ required: [8, 15].includes(departmentId) }]}
            >
              <S3Upload maxCount={8} pathName={fullPath} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default SalaryForm;
