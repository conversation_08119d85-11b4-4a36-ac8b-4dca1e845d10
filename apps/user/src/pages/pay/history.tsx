import { Link } from 'umi';
import { useState } from 'react';
import { useAntdTable } from 'ahooks';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Row,
  Space,
  Statistic,
  Table,
} from 'antd';
import dayjs from 'dayjs';
////
import SalaryApplyTypes from '@xly/configs/Salary/ApplyTypes.json';
import SalaryDepartments from '@xly/configs/Salary/Departments.json';
import { getAllSalaryRecords } from '@/services/request/salaryRecords';
import { useUserOptions } from '@/hooks/useApiOptions';
import { useUserInfo } from '@/hooks/useUserInfo';
import { formatterEn } from '@/utils/function';

export interface PayHistoryProps {}

const PayHistory: React.FC<PayHistoryProps> = () => {
  // status
  const [month, setMonth] = useState<dayjs.Dayjs | null>(dayjs());
  const { userInfo } = useUserInfo();
  const { userOptions } = useUserOptions();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    let sorter: any = {
      sortField: 'work_date',
      sortOrder: -1,
    };
    if (Array.isArray(pageData?.sorter?.field)) {
      sorter.sortField = pageData?.sorter?.field?.join('.');
    } else if (!!pageData?.sorter?.field) {
      sorter.sortField = pageData?.sorter?.field;
    } else {
      sorter.sortField = 'work_date';
    }
    if (pageData?.sorter?.order === 'ascend') {
      sorter.sortOrder = 1;
    }
    if (pageData?.sorter?.order === 'descend') {
      sorter.sortOrder = -1;
    }
    const data = await getAllSalaryRecords({
      user: userInfo?._id,
      page: 0,
      perPage: 9999,
      apply_status_in: JSON.stringify([3]),
      work_date_start: month?.startOf('M').format('YYYY-MM-DD'),
      work_date_end: month?.endOf('M').format('YYYY-MM-DD'),
      ...formData,
      ...sorter,
    });
    return {
      total: data?.totalCount,
      list: (data?.salaryRecords as API.SalaryRecord[]) || [],
    };
  };
  const salaryAPI = useAntdTable(getTableData, {});

  // action
  const handleMonth = (v: dayjs.Dayjs | null) => {
    setMonth(v);
    salaryAPI.search.submit();
  };

  // format
  const [finalSalary, workHours, restHours] = salaryAPI?.data?.list?.reduce(
    (temp, item) => {
      temp[0] += item.final_salary || 0;
      temp[1] += item.work_hours || 0;
      temp[2] += item.rest_hours || 0;
      return temp;
    },
    [0, 0, 0],
  ) || [0, 0, 0];

  return (
    <div className="w-screen h-screen p-6 box-border">
      <Card
        className="w-full h-full grid"
        title={<h2>新领域理工塾入职系统 - 往期⼯资⼀览</h2>}
        style={{
          gridTemplateRows: '100px 1fr',
        }}
        bodyStyle={{
          overflow: 'scroll',
        }}
        extra={
          <Link to="/">
            <Button type="primary">返回</Button>
          </Link>
        }
      >
        <div className="mt-2">
          <Space>
            <span className="text-2xl">
              {`${userInfo?.last_name_cn || ''} ${
                userInfo?.first_name_cn || ''
              }`}
            </span>
            <DatePicker
              allowClear={false}
              value={month}
              size="large"
              onChange={handleMonth}
              picker="month"
              format="YYYY年MM月"
            />
          </Space>
        </div>
        <Row>
          <Col span={6} className="p-8">
            <Statistic
              title="总工资"
              value={finalSalary}
              precision={2}
              suffix="円"
            />
          </Col>
          <Col span={6} className="p-8">
            <Statistic
              title="支付日(土日祝则顺延至下一个平日)"
              value={month?.add(1, 'M')?.format('YYYY年MM月10日')}
            />
          </Col>
          <Col span={6} className="p-8">
            <Statistic title="总劳动时间" value={workHours} suffix="小时" />
          </Col>
          <Col span={6} className="p-8">
            <Statistic title="总休息时间" value={restHours} suffix="小时" />
          </Col>
        </Row>
        <Table
          {...salaryAPI.tableProps}
          size="small"
          rowKey="_id"
          scroll={{ x: 2400, y: 'calc(100vh - 450px)' }}
          pagination={false}
        >
          <Table.Column
            sorter
            title="日期"
            width={100}
            dataIndex="work_date"
            render={(text) => dayjs(text)?.format('YYYY-MM-DD')}
          />
          <Table.Column
            sorter
            title="负责人"
            width={100}
            dataIndex="check_user"
            render={(value) =>
              userOptions?.find((item) => item?.value === value)?.label
            }
          />
          <Table.Column
            title="所属部⻔"
            width={160}
            sorter
            dataIndex="department_ids"
            render={([id1, id2]) => {
              const d1 = SalaryDepartments?.find(({ value }) => value === id1);
              const d2 = d1?.children?.find(({ value }) => value === id2);
              return d1?.label ? `${d1?.label}/${d2?.label}` : '';
            }}
          />
          <Table.Column width={160} title="工作内容" dataIndex="work_content" />
          <Table.Column
            title="计费方式"
            width={100}
            dataIndex="apply_type"
            render={(value) =>
              SalaryApplyTypes?.find((item) => item?.value === value)?.label
            }
          />
          <Table.Column
            title="工作单价"
            width={100}
            dataIndex="salary_per"
            render={(v) => formatterEn(v)}
          />
          <Table.ColumnGroup title="工作时间">
            <Table.Column
              width={100}
              title="开始时间"
              dataIndex="start_time_str"
            />
            <Table.Column
              width={100}
              title="结束时间"
              dataIndex="end_time_str"
            />
          </Table.ColumnGroup>
          <Table.Column width={100} title="人/件/字数" dataIndex="amount" />
          <Table.ColumnGroup title="交通费">
            <Table.Column width={100} title="起始站" dataIndex="travel_start" />
            <Table.Column width={100} title="终点站" dataIndex="travel_end" />
            <Table.Column
              title="往返金额"
              width={100}
              dataIndex="travel_fee"
              render={(v) => formatterEn(v)}
            />
          </Table.ColumnGroup>
          <Table.Column width={100} title="劳动时间" dataIndex="work_hours" />
          <Table.Column width={100} title="休息时间" dataIndex="rest_hours" />
          <Table.Column
            title="工作收入"
            width={100}
            dataIndex="final_salary"
            render={(v) => formatterEn(v)}
          />
          <Table.Column title="备注" dataIndex="memo" />
        </Table>
      </Card>
    </div>
  );
};

export default PayHistory;
