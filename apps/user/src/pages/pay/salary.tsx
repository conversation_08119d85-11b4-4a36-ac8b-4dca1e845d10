import { useEffect } from 'react';
import { Link, useNavigate } from 'umi';
import {
  Button,
  Card,
  Col,
  message,
  Modal,
  Row,
  Statistic,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import { useAntdTable } from 'ahooks';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { CopyOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
////
import SalaryApplyTypes from '@xly/configs/Salary/ApplyTypes.json';
import SalaryDepartments from '@xly/configs/Salary/Departments.json';
import ApplyStatus from '@xly/configs/Salary/ApplyStatus.json';
import { useForm } from '@/hooks/useSKForm';
import { useUserOptions } from '@/hooks/useApiOptions';
import { useUserInfo } from '@/hooks/useUserInfo';
import { formatterEn } from '@/utils/function';
import {
  createSalaryRecord,
  deleteSalaryRecordById,
  getAllSalaryRecords,
  applySalaryRecords,
  updateSalaryRecord,
} from '@/services/request/salaryRecords';
import SalaryForm from './components/SalaryForm';
import {
  fixFileListToUrl,
  fixUrlToFileList,
} from '@/pages/profile/components/S3Upload';

dayjs.extend(utc);
dayjs.extend(timezone);

export interface PaySalaryProps {}

const PaySalary: React.FC<PaySalaryProps> = () => {
  // state
  const navigate = useNavigate();
  const { userInfo } = useUserInfo();
  const { userOptions } = useUserOptions();
  const { formType, formProps, handleOpen } = useForm<API.SalaryRecord>();

  useEffect(() => {
    if (
      !userInfo?._id ||
      !userInfo?.birthday ||
      !userInfo?.ID_type ||
      !userInfo?.bank_account?.account_type
    ) {
      Modal.warning({
        title: '您的个人&账户信息不完全',
        content:
          '系统检测到您的个人&账户信息不完全，请补充完整以解锁工资申报功能。',
        onOk: () => {
          navigate('/profile/first-setting');
        },
      });
    }
  }, []);

  // api
  const getTableData = async (pageData: any, formData: any) => {
    let sorter: any = {};
    if (Array.isArray(pageData?.sorter?.field)) {
      sorter.sortField = pageData?.sorter?.field?.join('.');
    } else if (!!pageData?.sorter?.field) {
      sorter.sortField = pageData?.sorter?.field;
    } else {
      sorter.sortField = 'work_date';
    }
    if (pageData?.sorter?.order === 'ascend') {
      sorter.sortOrder = 1;
    }
    if (pageData?.sorter?.order === 'descend') {
      sorter.sortOrder = -1;
    }
    const data = await getAllSalaryRecords({
      user: userInfo?._id,
      page: 0,
      perPage: 99999,
      apply_status_in: JSON.stringify([1, 2, 4]),
      ...formData,
      ...sorter,
    });
    return {
      total: data?.totalCount,
      list: ((data?.salaryRecords as API.SalaryRecord[]) || [])?.filter(
        (item) => {
          if (item?.apply_status === 4) {
            return dayjs().add(-2, 'month').isBefore(dayjs(item?.check_date));
          }
          return true;
        },
      ),
    };
  };
  const salaryAPI = useAntdTable(getTableData, {});

  // format
  const finalSalary = salaryAPI?.data?.list?.reduce(
    (temp, item) => {
      temp[item.apply_status || 0] += item.final_salary || 0;
      temp[0] += item.final_salary || 0;
      return temp;
    },
    [0, 0, 0, 0, 0],
  ) || [0, 0, 0, 0, 0];

  const thisDay = dayjs().utc().tz('Asia/Tokyo').get('D');
  const hasUnPassData = salaryAPI.data?.list?.some(
    ({ apply_status, check_date }) =>
      apply_status === 4 &&
      dayjs
        .utc(check_date)
        .tz('Asia/Tokyo')
        .isAfter(dayjs().utc().tz('Asia/Tokyo').startOf('month')),
  );
  // 1-3日 可提交
  const isSubmitDay = [1, 2, 3].includes(thisDay);
  // 4-7日 有未通过件可提交
  const isReSubmitDay = [4, 5, 6, 7, 8, 9].includes(thisDay) && hasUnPassData;

  // action
  const handleSubmitAll = async () => {
    try {
      await applySalaryRecords({
        userId: userInfo?._id,
      });
      salaryAPI.search.submit();
    } catch (error) {
      console.log('Field:', error);
    }
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await createSalaryRecord({
          ...v,
          work_date: v?.work_date?.startOf('day').format('YYYY-MM-DD'),
          file_links: fixFileListToUrl(v?.file_links || []),
          user: userInfo?._id,
        });
        salaryAPI.search.submit();
      }
      if (formType === 'edit') {
        await updateSalaryRecord({
          ...v,
          work_date: v?.work_date?.startOf('day').format('YYYY-MM-DD'),
          apply_status: 1,
          file_links: fixFileListToUrl(v?.file_links || []),
          salaryRecordId: formProps?.dataSource?._id,
          user: userInfo?._id,
        });
        salaryAPI.search.submit();
      }
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  const handleAdd = () => {
    handleOpen({
      title: '新建工作',
      type: 'add',
      data: null,
    });
  };

  return (
    <div className="w-screen h-screen p-6 box-border">
      <Card
        className="w-full h-full grid"
        title={<h2>新领域理工塾入职系统 - ⼯资申报</h2>}
        style={{
          gridTemplateRows: '100px 1fr',
        }}
        bodyStyle={{
          overflow: 'scroll',
        }}
        extra={
          <Link to="/">
            <Button type="primary">返回</Button>
          </Link>
        }
        actions={[
          <Tooltip
            placement="topLeft"
            title={dayjs().format('仅提交YYYY年MM月以前数据')}
          >
            <Button
              type="primary"
              onClick={handleSubmitAll}
              // TODO：提交功能限制
              disabled={!isSubmitDay && !isReSubmitDay}
            >
              提交
            </Button>
          </Tooltip>,
        ]}
      >
        <SalaryForm
          options={{ SalaryApplyTypes, SalaryDepartments, userOptions }}
          type={formType}
          {...formProps}
          onSubmit={handleSubmit}
        />
        <Button type="primary" onClick={handleAdd}>
          新建
        </Button>
        <Row className="mt-4">
          <Col span={6} className="px-8">
            <Statistic
              title="总工资"
              value={finalSalary[0]}
              precision={2}
              suffix="円"
            />
          </Col>
          <Col span={6} className="px-8">
            <Statistic
              title="未提交"
              value={finalSalary[1]}
              precision={2}
              suffix="円"
            />
          </Col>
          <Col span={6} className="px-8">
            <Statistic
              title="已提交"
              value={finalSalary[2]}
              precision={2}
              suffix="円"
            />
          </Col>
          <Col span={6} className="px-8">
            <Statistic
              title="已驳回"
              value={finalSalary[4]}
              precision={2}
              suffix="円"
            />
          </Col>
        </Row>
        <Table
          className="mt-4"
          {...salaryAPI.tableProps}
          scroll={{ x: 2200, y: 'calc(100vh - 460px)' }}
          size="small"
          rowKey="_id"
          pagination={false}
        >
          <Table.Column
            width={100}
            sorter
            fixed="left"
            title="申报状态"
            dataIndex="apply_status"
            render={(apply_status) => (
              <Tag color={ApplyStatus?.[apply_status - 1]?.color}>
                {ApplyStatus?.[apply_status - 1]?.label}
              </Tag>
            )}
          />
          <Table.Column
            sorter
            title="日期"
            width={100}
            dataIndex="work_date"
            defaultSortOrder="descend"
            render={(text) => dayjs(text)?.format('YYYY-MM-DD')}
          />
          <Table.Column
            sorter
            title="负责人"
            width={100}
            dataIndex="check_user"
            render={(value) =>
              userOptions?.find((item) => item?.value === value)?.label
            }
          />
          <Table.Column
            sorter
            title="所属部⻔"
            width={160}
            dataIndex="department_ids"
            render={([id1, id2]) => {
              const d1 = SalaryDepartments?.find(({ value }) => value === id1);
              const d2 = d1?.children?.find(({ value }) => value === id2);
              return d1?.label ? `${d1?.label}/${d2?.label}` : '';
            }}
          />
          <Table.Column width={160} title="工作内容" dataIndex="work_content" />
          <Table.Column
            title="计费方式"
            width={100}
            dataIndex="apply_type"
            render={(value) =>
              SalaryApplyTypes?.find((item) => item?.value === value)?.label
            }
          />
          <Table.Column
            title="工作单价"
            width={100}
            dataIndex="salary_per"
            render={(v) => formatterEn(v)}
          />
          <Table.ColumnGroup title="工作时间">
            <Table.Column
              width={100}
              title="开始时间"
              dataIndex="start_time_str"
            />
            <Table.Column
              width={100}
              title="结束时间"
              dataIndex="end_time_str"
            />
          </Table.ColumnGroup>
          <Table.Column width={100} title="人/件/字数" dataIndex="amount" />
          <Table.ColumnGroup title="交通费">
            <Table.Column width={100} title="起始站" dataIndex="travel_start" />
            <Table.Column width={100} title="终点站" dataIndex="travel_end" />
            <Table.Column
              width={100}
              title="往返金额"
              dataIndex="travel_fee"
              render={(v) => formatterEn(v)}
            />
          </Table.ColumnGroup>
          <Table.Column width={100} title="劳动时间" dataIndex="work_hours" />
          <Table.Column width={100} title="休息时间" dataIndex="rest_hours" />
          <Table.Column
            fixed="right"
            width={100}
            title="工作收入"
            dataIndex="final_salary"
            render={(v) => formatterEn(v)}
          />
          <Table.Column fixed="right" title="备注" dataIndex="memo" />
          <Table.ColumnGroup title="操作">
            <Table.Column
              title="复制"
              fixed="right"
              width={50}
              render={(row) => {
                const handleCopy = () => {
                  handleOpen({
                    title: '复制并新建工作',
                    type: 'add',
                    data: {
                      check_user: row?.check_user,
                      work_date: row?.work_date,
                      department_id: row?.department_id,
                      apply_type: row?.apply_type,
                      ...row,
                    },
                  });
                };
                return (
                  <Button size="small" onClick={handleCopy}>
                    <CopyOutlined />
                  </Button>
                );
              }}
            />
            <Table.Column
              title="编辑"
              fixed="right"
              width={50}
              render={(row) => {
                const handleEdit = () => {
                  handleOpen({
                    title: '修改工作',
                    type: 'edit',
                    data: {
                      ...row,
                      file_links: fixUrlToFileList(row?.file_links || []),
                    },
                  });
                };

                return (
                  <Button
                    size="small"
                    onClick={handleEdit}
                    disabled={[2, 3, 4].includes(row?.apply_status)}
                  >
                    <EditOutlined />
                  </Button>
                );
              }}
            />
            <Table.Column
              title="删除"
              fixed="right"
              width={50}
              render={(row) => {
                const handleDelete = () => {
                  Modal.confirm({
                    centered: true,
                    title: '删除',
                    content: '确认删除本条申报，删除后无法恢复。',
                    onOk: async () => {
                      await deleteSalaryRecordById({
                        salaryRecordId: row?._id,
                      });
                      salaryAPI.search.submit();
                    },
                    okText: '确认',
                    cancelText: '取消',
                  });
                };
                return (
                  <Button
                    size="small"
                    onClick={handleDelete}
                    disabled={[2, 4].includes(row?.apply_status)}
                  >
                    <DeleteOutlined />
                  </Button>
                );
              }}
            />
          </Table.ColumnGroup>
        </Table>
      </Card>
    </div>
  );
};

export default PaySalary;
