import { useRequest } from 'ahooks';
import { Button, Card, Form, Input, message } from 'antd';
import { useNavigate } from 'umi';
////
import { createUser, userLogin } from '@/services/request/user';
import { useUserInfo } from '@/hooks/useUserInfo';

export interface registerProps {}

const register: React.FC<registerProps> = () => {
  // state
  const navigate = useNavigate();
  const { updateUserInfo } = useUserInfo();
  const createUserApi = useRequest(createUser, {
    manual: true,
  });
  const userLoginApi = useRequest(userLogin, {
    manual: true,
  });

  // action
  const onBack = () => {
    navigate('/');
  };

  const onFinish = async (values: any) => {
    try {
      const res = await createUserApi.runAsync(values);
      if (!res?._id) throw res;
      message.info('注册成功，请登陆个人信息!');
      const res2 = await userLoginApi.runAsync(values);
      if (!res2?._id) throw res2;
      updateUserInfo(res);
      navigate('/profile/first-setting');
    } catch (error: any) {
      message.error(error?.message || 'error');
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className="flex h-screen">
      <Card
        className="m-auto w-96"
        title="注册 - 新领域理工塾入职系统"
        extra={<Button onClick={onBack}>返回</Button>}
      >
        <Form
          name="basic"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={{ remember: true }}
          onFinish={onFinish}
          labelAlign="left"
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            label="邮箱"
            name="email"
            rules={[{ required: true, message: '请输入邮箱!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item
            label="密码确认"
            name="confirm"
            dependencies={['password']}
            rules={[
              { required: true, message: '请输入密码!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('二次密码不一致!'));
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Button type="primary" htmlType="submit">
              注册
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default register;
