import { Link } from 'umi';
import { useState } from 'react';
import { useRequest } from 'ahooks';
import { Button, Card, Form, message } from 'antd';
import dayjs from 'dayjs';
////
import PayForm from './components/PayForm';
import BasicForm from './components/BasicForm';
import DocumentsForm from './components/DocumentsForm';
import ResetPasswordForm from './components/ResetPasswordForm';
import { resetUserPassword, updateUser } from '@/services/request/user';
import { useUserInfo } from '@/hooks/useUserInfo';
import { fixFileListToUrl, fixUrlToFileList } from './components/S3Upload';

export interface ProfileSettingProps {}

const tabList = [
  {
    key: 'basic',
    tab: '基础信息',
  },
  {
    key: 'documents',
    tab: '证件信息',
  },
  {
    key: 'pay',
    tab: '账户信息',
  },
  {
    key: 'reset',
    tab: '重设密码',
  },
];

const contentList: Record<string, React.ReactNode> = {
  pay: <PayForm />,
  basic: <BasicForm />,
  documents: <DocumentsForm />,
  reset: <ResetPasswordForm />,
};

const ProfileSetting: React.FC<ProfileSettingProps> = () => {
  // state
  const [activeTabKey, setActiveTabKey] = useState<string>('basic');
  const [form] = Form.useForm();
  const { userInfo, updateUserInfo } = useUserInfo({
    manual: false,
    onSuccess: (data: API.User) => {
      form.setFieldsValue({
        ...data,
        ID_expiry_date: data?.ID_expiry_date
          ? dayjs(data?.ID_expiry_date)
          : null,
        ID_images: fixUrlToFileList(data?.ID_images),
        bank_account: data?.bank_account
          ? {
              ...data?.bank_account,
              bank_account_images: fixUrlToFileList(
                data?.bank_account?.bank_account_images,
              ),
              CN_ID_images: fixUrlToFileList(data?.bank_account?.CN_ID_images),
            }
          : null,
      });
    },
  });

  console.log(userInfo);
  // api
  const resetUserPasswordAPI = useRequest(resetUserPassword, {
    manual: true,
  });
  const updateUserAPI = useRequest(updateUser, {
    manual: true,
  });

  // action
  const onTabChange = (key: string) => {
    setActiveTabKey(key);
  };
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (activeTabKey === 'reset') {
        const res = await resetUserPasswordAPI.runAsync(values);
        message.warning(res?.message);
      } else if (activeTabKey === 'pay') {
        const nesUser = {
          userId: userInfo?._id,
          ...values,
          bank_account: values?.bank_account
            ? {
                ...values?.bank_account,
                bank_account_images: fixFileListToUrl(
                  values?.bank_account?.bank_account_images,
                ),
                CN_ID_images: fixFileListToUrl(
                  values?.bank_account?.CN_ID_images,
                ),
              }
            : null,
        };
        const res = await updateUserAPI.runAsync(nesUser);
        updateUserInfo(nesUser);
        message.warning(res?.message);
      } else if (activeTabKey === 'documents') {
        const res = await updateUserAPI.runAsync({
          userId: userInfo?._id,
          ...values,
          ID_images: fixFileListToUrl(values?.ID_images),
        });
        message.warning(res?.message);
      } else if (activeTabKey === 'basic') {
        const res = await updateUserAPI.runAsync({
          userId: userInfo?._id,
          ...values,
        });
        message.warning(res?.message);
      }
    } catch (err: any) {
      message.error(err?.message);
      console.log('Field:', err);
    }
  };

  return (
    <div className="w-screen h-screen p-6 box-border">
      <Card
        className="w-full h-full grid"
        title={<h2>新领域理工塾入职系统 - 个⼈&账户信息</h2>}
        style={{
          gridTemplateRows: '150px 1fr 64px',
        }}
        bodyStyle={{
          overflow: 'scroll',
        }}
        extra={
          <Link to="/">
            <Button type="primary">返回</Button>
          </Link>
        }
        tabList={tabList}
        activeTabKey={activeTabKey}
        onTabChange={onTabChange}
        actions={[
          <Button type="primary" onClick={handleSubmit}>
            提交
          </Button>,
        ]}
      >
        <Form
          name="UserSettingForm"
          form={form}
          disabled={!!userInfo.status}
          labelCol={{ span: 12 }}
          wrapperCol={{ span: 12 }}
          onValuesChange={(values) => {
            if (values.ID_type) {
              form.validateFields(['ID_images']); // 校验 ID_images
            }
          }}
          validateMessages={{ required: "'${label}' 是必填项" }}
          autoComplete="off"
        >
          {contentList[activeTabKey]}
        </Form>
      </Card>
    </div>
  );
};

export default ProfileSetting;
