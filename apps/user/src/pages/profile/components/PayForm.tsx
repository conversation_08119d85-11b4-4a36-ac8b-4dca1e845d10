import { Row, Col, Form, Input, Select } from 'antd';
////
import { useUserInfo } from '@/hooks/useUserInfo';
import S3Upload from './S3Upload';

export interface PayFormProps {}

const PayForm: React.FC<PayFormProps> = () => {
  const { userInfo } = useUserInfo();
  const form = Form.useFormInstance();
  const accountType = Form.useWatch(['bank_account', 'account_type'], form);

  return (
    <Row>
      <Col flex="600px">
        <Form.Item
          label="⼯资收款账户类型"
          name={['bank_account', 'account_type']}
          rules={[{ required: true }]}
        >
          <Select
            style={{ width: 200 }}
            options={[
              { value: 1, label: '⽇本银⾏账户' },
              { value: 2, label: '中国银⾏账户' },
              { value: 3, label: '⽀付宝' },
            ]}
            onChange={(v) => {
              if (v === 1) return;
              if (v === 3) {
                form.setFieldValue(['bank_account', 'bank_name'], '⽀付宝');
                form.setFieldValue(
                  ['bank_account', 'bank_branch_name'],
                  '⽀付宝',
                );
              }
              if (form.getFieldValue('ID_type') === 2) {
                form.setFieldValue(
                  ['bank_account', 'CN_ID_no'],
                  form.getFieldValue('ID_no'),
                );
                form.setFieldValue(
                  ['bank_account', 'CN_ID_images'],
                  form.getFieldValue('ID_images'),
                );
              }
            }}
          />
        </Form.Item>
        <Form.Item
          label="银⾏名称"
          name={['bank_account', 'bank_name']}
          rules={[{ required: true }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="⽀店名"
          name={['bank_account', 'bank_branch_name']}
          rules={[{ required: true }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="⼝座番号"
          name={['bank_account', 'account_no']}
          rules={[{ required: true }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="⼝座名义"
          name={['bank_account', 'holder_name']}
          rules={[{ required: true }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="收款账户是否为本⼈"
          name={['bank_account', 'is_self']}
          rules={[{ required: accountType !== 1 }]}
        >
          <Select
            style={{ width: 200 }}
            options={[
              { value: true, label: '是' },
              { value: false, label: '否' },
            ]}
            onChange={(v) => {
              if (v) {
                form.setFieldValue(
                  ['bank_account', 'payee_name'],
                  form.getFieldValue(['bank_account', 'holder_name']),
                );
              }
            }}
          />
        </Form.Item>
        <Form.Item
          label="收款⼈姓名"
          name={['bank_account', 'payee_name']}
          rules={[{ required: accountType !== 1 }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="登录银⾏账户上传"
          name={['bank_account', 'bank_account_images']}
          valuePropName="fileList"
          getValueFromEvent={(e) => {
            if (Array.isArray(e)) {
              return e;
            }
            return e?.fileList;
          }}
          rules={[
            { required: accountType !== 3 },
            {
              validator(_, value) {
                if (accountType !== 3 && value.length !== 1) {
                  return Promise.reject(
                    new Error('银⾏卡必须上传正反面共计 1 张!'),
                  );
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <S3Upload
            maxCount={1}
            pathName={'bank_account_images/' + userInfo?._id}
          />
        </Form.Item>

        <Form.Item
          label="收款⼈⼆代居⺠身份证号"
          name={['bank_account', 'CN_ID_no']}
          rules={[{ required: accountType !== 1 }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="收款⼈⼆代居⺠身份证上传"
          name={['bank_account', 'CN_ID_images']}
          valuePropName="fileList"
          getValueFromEvent={(e) => {
            if (Array.isArray(e)) {
              return e;
            }
            return e?.fileList;
          }}
        >
          <S3Upload maxCount={2} pathName={'CN_ID_images/' + userInfo?._id} />
        </Form.Item>
      </Col>
    </Row>
  );
};

export default PayForm;
