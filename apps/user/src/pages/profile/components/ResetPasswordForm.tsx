import { Row, Col, Form, Input } from 'antd';

export interface ResetPasswordFormProps {}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = () => {
  return (
    <Row>
      <Col flex="600px">
        <Form.Item label="邮箱" name="email" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item
          label="旧密码"
          name="oldPassword"
          rules={[{ required: true }]}
        >
          <Input.Password disabled={false} />
        </Form.Item>
        <Form.Item
          label="新密码"
          name="newPassword"
          rules={[
            { required: true },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('oldPassword') !== value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('新密码与旧密码一致!'));
              },
            }),
          ]}
        >
          <Input.Password disabled={false} />
        </Form.Item>
        <Form.Item
          label="新密码确认"
          name="confirm"
          dependencies={['newPassword']}
          rules={[
            { required: true },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('二次密码不一致!'));
              },
            }),
          ]}
        >
          <Input.Password disabled={false} />
        </Form.Item>
      </Col>
    </Row>
  );
};

export default ResetPasswordForm;
