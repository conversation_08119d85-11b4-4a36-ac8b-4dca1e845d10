import { Upload } from 'antd';
import { upload_image } from '@/services/useAWS_S3';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';

export interface S3UploadProps extends UploadProps {
  pathName: string;
  maxCount?: number;
}

export const fixUrlToFileList = (urls: any[] = []) => {
  return urls?.map((url) => ({
    uid: url,
    name: url.substring(url.lastIndexOf('/') + 1),
    status: 'done',
    url,
  }));
};

export const fixFileListToUrl = (fileList: any[] = []) => {
  return fileList?.map((item) => item?.url || item?.response?.url || null);
};

const S3Upload: React.FC<S3UploadProps> = (props) => {
  const customRequest = async ({ file, onError, onSuccess }: any) => {
    try {
      const fileName = file.name;
      const fileExtention = fileName.substring(fileName.lastIndexOf('.') + 1);
      const blob = file.slice(0, file.size, file.type);
      const renamedFile = new File(
        [blob],
        props.pathName + '/' + Date.now() + '.' + fileExtention,
        { type: file.type },
      );
      const res = await upload_image(renamedFile);
      onSuccess(res, file);
    } catch (err: any) {
      onError();
      console.log(err?.code);
      console.log(err?.message);
    }
  };

  const onPreview = async (file: UploadFile) => {
    let src = file.url as string;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj as RcFile);
        reader.onload = () => resolve(reader.result as string);
      });
    }
    const image = new Image();
    image.src = src;
    image.width = 500;
    const imgWindow = window.open(src);
    imgWindow?.document.write(image.outerHTML);
  };

  const length = props?.fileList?.length || 0;
  const maxCount = props?.maxCount || 1;

  return (
    <Upload
      accept="image/*,.pdf"
      maxCount={maxCount}
      listType="picture-card"
      onPreview={onPreview}
      customRequest={customRequest}
      {...props}
    >
      {length < maxCount && '+ Upload'}
    </Upload>
  );
};

export default S3Upload;
