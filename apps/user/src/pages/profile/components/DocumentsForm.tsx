import { useUserInfo } from '@/hooks/useUserInfo';
import { Row, Col, Form, Input, Select, DatePicker } from 'antd';
////
import S3Upload from './S3Upload';

export interface DocumentsFormProps {}

const DocumentsForm: React.FC<DocumentsFormProps> = () => {
  const { userInfo } = useUserInfo();
  const form = Form.useFormInstance();
  const idType = Form.useWatch('ID_type', form);
  const maxCount = idType === 3 ? 1 : 2; // 护照时限制1张，其他限制两张

  return (
    <Row>
      <Col flex="600px">
        <Form.Item
          label="身份证件类型选择"
          name="ID_type"
          rules={[{ required: true }]}
        >
          <Select
            style={{ width: 250 }}
            placeholder="有在留卡则必选在留卡"
            options={[
              { value: 1, label: '在留卡' },
              { value: 2, label: '⼆代居⺠身份证' },
              { value: 3, label: '护照' },
            ]}
          />
        </Form.Item>
        <Form.Item
          label="登录身份证件上传"
          name="ID_images"
          valuePropName="fileList"
          getValueFromEvent={(e) => {
            if (Array.isArray(e)) {
              return e;
            }
            return e?.fileList;
          }}
          rules={[
            { required: true },
            ({ getFieldValue }) => ({
              validator(_, value) {
                const ID_type = getFieldValue('ID_type');
                if (ID_type === 3 && value.length !== 1) {
                  return Promise.reject(new Error('护照必须上传正面照 1 张!'));
                }
                if ([1, 2].includes(ID_type) && value.length !== 2) {
                  return Promise.reject(
                    new Error('身份证件必须上传正反面共计 2 张!'),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <S3Upload
            maxCount={maxCount}
            pathName={'ID_images/' + userInfo?._id}
          />
        </Form.Item>
        <Form.Item
          label="国籍"
          name="nationality"
          rules={[{ required: idType === 2 }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="证件号"
          name="ID_no"
          rules={[{ required: idType === 2 }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="证件有效期限"
          name="ID_expiry_date"
          rules={[{ required: idType === 2 }]}
        >
          <DatePicker />
        </Form.Item>
        <Form.Item
          label="在留资格"
          name="ID_license"
          rules={[{ required: idType === 1 }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="资格外活动许可"
          name="is_activity_permission"
          rules={[{ required: idType === 1 }]}
        >
          <Select
            style={{ width: 200 }}
            options={[
              { value: true, label: '有' },
              { value: false, label: '无' },
            ]}
          />
        </Form.Item>
        <Form.Item
          label="抚养"
          name="is_dependents"
          rules={[{ required: true }]}
        >
          <Select
            style={{ width: 200 }}
            options={[
              { value: true, label: '有' },
              { value: false, label: '无' },
            ]}
          />
        </Form.Item>
      </Col>
      <Col flex="600px">
        <Form.Item label="个⼈番号" name="my_number">
          <Input />
        </Form.Item>
        <Form.Item
          label="现住址"
          name="address"
          rules={[{ required: idType === 2 }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="证件上住址所在地"
          name="address_of_license"
          rules={[{ required: idType === 2 }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="电话号码"
          name="tel"
          rules={[{ required: idType === 2 }]}
        >
          <Input />
        </Form.Item>
        <Form.Item label="（预）毕业院校名" name="graduate_university">
          <Input />
        </Form.Item>
        <Form.Item label="专业名" name="faculty">
          <Input />
        </Form.Item>
        <Form.Item label="（预）毕业时间" name="graduate_date">
          <Input />
        </Form.Item>
        <Form.Item label="（预）取得学位" name="degree">
          <Input />
        </Form.Item>
      </Col>
    </Row>
  );
};

export default DocumentsForm;
