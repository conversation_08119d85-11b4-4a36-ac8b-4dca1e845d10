import { Link } from 'umi';
import { useRequest } from 'ahooks';
import { <PERSON><PERSON>, Card, Divider, Form, message } from 'antd';
import dayjs from 'dayjs';
////
import PayForm from './components/PayForm';
import BasicForm from './components/BasicForm';
import DocumentsForm from './components/DocumentsForm';
import { updateUser } from '@/services/request/user';
import { useUserInfo } from '@/hooks/useUserInfo';
import { fixFileListToUrl, fixUrlToFileList } from './components/S3Upload';

export interface ProfileSettingProps {}

const ProfileSetting: React.FC<ProfileSettingProps> = () => {
  // state
  const [form] = Form.useForm();
  const { userInfo, updateUserInfo } = useUserInfo({
    onSuccess: (data: API.User) => {
      form.setFieldsValue({
        ...data,
        ID_expiry_date: data?.ID_expiry_date
          ? dayjs(data?.ID_expiry_date)
          : null,
        ID_images: fixUrlToFileList(data?.ID_images),
        bank_account: data?.bank_account
          ? {
              ...data?.bank_account,
              bank_account_images: fixUrlToFileList(
                data?.bank_account?.bank_account_images,
              ),
              CN_ID_images: fixUrlToFileList(data?.bank_account?.CN_ID_images),
            }
          : null,
      });
    },
  });

  // api
  const updateUserAPI = useRequest(updateUser, {
    manual: true,
  });

  // action
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const newUser = {
        userId: userInfo?._id,
        ...values,
        ID_images: fixFileListToUrl(values?.ID_images),
        bank_account: values?.bank_account
          ? {
              ...values?.bank_account,
              bank_account_images: fixFileListToUrl(
                values?.bank_account?.bank_account_images,
              ),
              CN_ID_images: fixFileListToUrl(
                values?.bank_account?.CN_ID_images,
              ),
            }
          : null,
      };
      const res = await updateUserAPI.runAsync(newUser);
      updateUserInfo(newUser);
      message.warning(res?.message);
    } catch (err: any) {
      message.error(err?.message);
      console.log('Field:', err);
    }
  };

  return (
    <div className="w-screen h-screen p-6 box-border">
      <Card
        className="w-full h-full grid"
        title={<h2>新领域理工塾入职系统</h2>}
        style={{
          gridTemplateRows: '150px 1fr 64px',
        }}
        tabList={[{ key: 'basic', tab: '个⼈&账户信息完善' }]}
        bodyStyle={{
          overflow: 'scroll',
        }}
        extra={
          <Link to="/">
            <Button type="primary">返回</Button>
          </Link>
        }
        actions={[
          <Button type="primary" onClick={handleSubmit}>
            提交
          </Button>,
        ]}
      >
        <Form
          name="UserSettingForm"
          form={form}
          labelCol={{ span: 12 }}
          wrapperCol={{ span: 12 }}
          onValuesChange={(values) => {
            if (values.ID_type) {
              form.validateFields(['ID_images']); // 校验 ID_images
            }
          }}
          validateMessages={{ required: "'${label}' 是必填项" }}
          autoComplete="off"
        >
          <Divider orientation="left">基础信息</Divider>
          <BasicForm />
          <Divider orientation="left">证件信息</Divider>
          <DocumentsForm />
          <Divider orientation="left">账户信息</Divider>
          <PayForm />
        </Form>
      </Card>
    </div>
  );
};

export default ProfileSetting;
