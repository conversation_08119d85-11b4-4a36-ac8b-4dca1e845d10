import { checkUserLogin, removeUserInfo } from '@/services/useStorage';
import { Button } from 'antd';
import { Link } from 'umi';

interface LinkButtonProps {
  to: string;
  text: string;
}
const LinkButton: React.FC<LinkButtonProps> = (props) => {
  return (
    <div>
      <Link to={props.to}>
        <Button size="large" block className="mb-6">
          {props?.text}
        </Button>
      </Link>
    </div>
  );
};

export interface HomePageProps {}

const HomePage: React.FC<HomePageProps> = () => {
  // state
  const isLogin = checkUserLogin();

  // action
  const handleLogout = () => {
    removeUserInfo();
    window.location.reload();
  };

  return (
    <div className="flex flex-col h-screen justify-center items-center text-center">
      <h2 className="text-white mb-10">新领域理工塾入职系统</h2>
      {isLogin ? (
        <div style={{ width: 200 }}>
          <LinkButton to="/profile/setting" text="个⼈&账户信息" />
          <LinkButton to="/pay/salary" text="⼯资申报" />
          <LinkButton to="/pay/history" text="往期⼯资⼀览" />
          <Button size="large" block className="mb-6" onClick={handleLogout}>
            登出
          </Button>
        </div>
      ) : (
        <div style={{ width: 200 }}>
          <LinkButton to="/account/login" text="登陆" />
          <LinkButton to="/account/register" text="注册" />
          <LinkButton to="/account/forget" text="忘记密码" />
        </div>
      )}
    </div>
  );
};

export default HomePage;
