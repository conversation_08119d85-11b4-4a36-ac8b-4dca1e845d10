import { useContext } from 'react';
import { useRequest } from 'ahooks';
////
import { GlobalContext } from '@/store/global';
import { getUserById } from '@/services/request/user';
import {
  getUserInfo,
  setUserInfo,
  removeUserInfo,
} from '@/services/useStorage';

export const useUserInfo = (opt?: any) => {
  const user = getUserInfo();
  const [state, dispatch] = useContext(GlobalContext);
  const userAPI = useRequest(
    async () => await getUserById({ userId: user?._id as string }),
    {
      manual: true,
      ...opt,
      onSuccess: (user) => {
        updateUserInfo(user);
        opt?.onSuccess?.(user);
      },
    },
  );

  const logout = () => {
    removeUserInfo();
    dispatch({ type: 'logout' });
  };

  const updateUserInfo = (v: any) => {
    setUserInfo(v);
    dispatch({ type: 'fetchUser', payload: v });
  };

  return {
    userInfo: state.userInfo || user || userAPI.data,
    logout,
    updateUserInfo,
    userAPI,
  };
};
