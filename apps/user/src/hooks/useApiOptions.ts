import { useMemo } from 'react';
import { useRequest } from 'ahooks';
////
import { getAllUsers } from '@/services/request/user';

interface UseUserOptions {
  params?: Partial<API.User>;
}

export const useUserOptions = (opt?: UseUserOptions) => {
  const params = opt?.params || {};
  const { data } = useRequest(
    () => getAllUsers({ page: 0, perPage: 99999, ...params }),
    {
      cacheKey: 'userOpts',
    },
  );
  const userOptions: { value: string; label: string; [key: string]: any }[] =
    useMemo(() => {
      return (
        data?.users?.map((item: any) => ({
          value: item?._id,
          label: item?.last_name_cn + item?.first_name_cn || '未知',
          ...item,
        })) || []
      );
    }, [data?.totalCount]);

  return { userOptions };
};
