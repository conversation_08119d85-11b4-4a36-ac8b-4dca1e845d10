import { history } from 'umi';

import { checkUserLogin, removeUserInfo } from '@/services/useStorage';

/**
 *
 * 路由权限判定, 每次切换路由都会触发
 */
export function onRouteChange() {
  const { pathname } = history.location;
  //初始化localStorage中的登陆信息
  const isLogin = checkUserLogin();

  //清除错误登陆信息
  if (!isLogin) {
    removeUserInfo();
  }

  //路由登陆判定
  if (!isLogin) {
    if (!pathname.startsWith('/account')) {
      if (pathname !== '/') {
        return history.replace('/account/login');
      }
    }
  }
}

// /**
//  *
//  * 初始化应用store，返回数据至useModel('@@initialState')
//  * @returns { userLogin }
//  */
// export async function getInitialState() {
//   //初始化登陆数据,获取访问权限等
//   return {};
// }
