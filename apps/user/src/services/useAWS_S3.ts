import AWS from 'aws-sdk';

const S3 = process.env?.S3 as any;
const BUCKET = S3?.S3_BUCKET;
const REGION = S3?.REGION;
const IDENTITY_POOL_ID = S3?.IDENTITY_POOL_ID;
export const BASIC_PATH = S3?.BASIC_PATH;

if (IDENTITY_POOL_ID && REGION) {
  AWS.config.region = REGION;
  AWS.config.credentials = new AWS.CognitoIdentityCredentials({
    IdentityPoolId: IDENTITY_POOL_ID,
  });
}

const s3 = new AWS.S3();

export const upload_image = async (file: any) => {
  try {
    const params = {
      Bucket: BUCKET,
      Key: file.name,
      ContentType: file.type,
      Body: file,
    };
    const res = await s3.putObject(params).promise();
    if (res.$response?.error) {
      throw res.$response?.error;
    }
    return {
      name: file.name,
      url: BASIC_PATH + file.name,
    };
  } catch (error) {
    throw error;
  }
};
