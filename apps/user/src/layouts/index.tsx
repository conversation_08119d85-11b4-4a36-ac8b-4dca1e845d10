import { Outlet } from 'umi';
import { ConfigProvider } from 'antd';
import 'dayjs/locale/zh-cn';
import locale from 'antd/locale/zh_CN';
////
import GlobalStore from '@/store/global';

export default function Layout() {
  return (
    <ConfigProvider
      locale={locale}
      theme={{
        token: {
          colorPrimary: '#253970',
        },
      }}
    >
      <GlobalStore>
        <Outlet />
      </GlobalStore>
    </ConfigProvider>
  );
}
