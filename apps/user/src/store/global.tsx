import { createContext, useReducer } from 'react';

type GlobalState = {
  userInfo: API.User | null;
  access: {};
};
type GlobalActions =
  | {
      type: 'fetchUser';
      payload: API.User;
    }
  | { type: 'logout' };
type GlobalContext = [GlobalState, React.Dispatch<GlobalActions>];

const globalState: GlobalState = {
  userInfo: null,
  access: {},
};
const globalReducer = (state: GlobalState, action: GlobalActions) => {
  switch (action.type) {
    case 'fetchUser':
      return {
        ...state,
        userInfo: action.payload,
        access: {},
      };
    case 'logout':
      return globalState;
    default:
      return state;
  }
};

export const GlobalContext = createContext<GlobalContext>([] as any);

const GlobalStore: React.FC<{ children: React.ReactNode }> = (props) => {
  const [state, dispatch] = useReducer(globalReducer, globalState);
  return (
    <GlobalContext.Provider value={[state, dispatch]}>
      {props.children}
    </GlobalContext.Provider>
  );
};

export default GlobalStore;
