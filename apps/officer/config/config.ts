import { defineConfig } from 'umi';

export default defineConfig({
  npmClient: 'yarn',
  hash: true,
  history: {
    type: 'hash',
  },
  title: '新领域理⼯塾-管理系统',

  // favicon: 'https://console.weixin-jp.com/assets/favicon.png',
  // 打包优化
  ignoreMomentLocale: true,
  // externals: {
  //   react: 'React',
  //   'react-dom': 'ReactDOM',
  // },

  // 引入被 external 库的 scripts
  // 区分 development 和 production，使用不同的产物
  // scripts:
  //   process.env.NODE_ENV === 'development'
  //     ? [
  //         'https://gw.alipayobjects.com/os/lib/react/18.2.0/umd/react.development.js',
  //         'https://gw.alipayobjects.com/os/lib/react-dom/18.2.0/umd/react-dom.development.js',
  //       ]
  //     : [
  //         'https://gw.alipayobjects.com/os/lib/react/18.2.0/umd/react.production.min.js',
  //         'https://gw.alipayobjects.com/os/lib/react-dom/18.2.0/umd/react-dom.production.min.js',
  //       ],
  tailwindcss: {},
  plugins: ['@umijs/plugins/dist/tailwindcss'],
});
