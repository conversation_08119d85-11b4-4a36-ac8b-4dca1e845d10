import { Outlet, useLocation } from 'umi';
import { ConfigProvider } from 'antd';
import 'dayjs/locale/zh-cn';
import locale from 'antd/locale/zh_CN';
////
import BasicLayout from './BasicLayout';
import GlobalStore from '@/store/global';

export default function Layout() {
  const location = useLocation();
  const isFull = ['/account', '/teaching_research/share'].some((item: any) =>
    location?.pathname?.startsWith(item),
  );

  return (
    <ConfigProvider
      locale={locale}
      theme={{
        token: {
          colorPrimary: '#253970',
        },
      }}
    >
      <GlobalStore>
        {isFull && <Outlet />}
        {!isFull && (
          <BasicLayout>
            <Outlet />
          </BasicLayout>
        )}
      </GlobalStore>
    </ConfigProvider>
  );
}
