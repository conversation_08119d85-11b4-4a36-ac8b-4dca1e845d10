import { Button, Card, Layout, Menu } from 'antd';
import { useLocation, useNavigate } from 'umi';
////
import { useUserInfo } from '@/hooks/useUserInfo';
import { useAccess } from '@/hooks/useAccess';

export interface BasicLayoutProps {
  children: React.ReactNode;
}
const items = [
  {
    key: `/pay`,
    label: `工资审核系统`,
    children: [
      { key: '/pay/review', label: '审核工资' },
      { key: '/pay/sub_review', label: '转让审核' },
      { key: '/pay/report', label: '工资报表' },
    ],
  },
  {
    key: `/student`,
    label: `学生信息系统`,
    children: [
      {
        key: '/student/consult',
        type: 'group',
        label: '咨询管理',
        children: [
          { key: '/student/consult/check', label: '查重' },
          { key: '/student/consult/todo', label: '管理表' }
        ],
      },
      {
        key: '/student/sale',
        type: 'group',
        label: '销售管理',
        children: [
          { key: '/student/sale/todo', label: '待处理' },
          { key: '/student/sale/list', label: '管理表' },
        ],
      },
      {
        key: '/student/graduate',
        type: 'group',
        label: '学生管理',
        children: [
          { key: '/student/graduate/todo', label: '待处理' },
          { key: '/student/graduate/list', label: '管理表' },
        ],
      },
      {
        key: '/student/vip',
        type: 'group',
        label: 'VIP管理',
        children: [
          { key: '/student/vip/list', label: 'VIP处理' },
          { key: '/student/vip/status', label: '管理表' },
        ],
      },
      {
        key: '/student/class-teacher',
        type: 'group',
        label: '班主任管理',
        children: [
          { key: '/student/class-teacher/student-list', label: '当前学生' },
          { key: '/student/class-teacher/student-history', label: '过去学生' },
        ],
      },
    ],
  },
  {
    key: '/teaching_research',
    label: '教研系统',
    children: [
      { key: '/teaching_research/todo', label: '今日Todo' },
      { key: '/teaching_research/list', label: '排课管理' },
      { key: '/teaching_research/zoom', label: 'ZOOM账号管理' },
    ],
  },
  {
    key: '/teacher',
    label: '教师系统',
    children: [
      { key: '/teacher/todo', label: '入职中' },
      { key: '/teacher/list', label: '管理表' },
    ],
  },
];
const itemOpenKeys = [
  '/pay',
  '/student',
  '/teacher',
  '/teaching_research',
];
const itemKeys = [
  '/pay/review',
  '/pay/sub_review',
  '/pay/report',
  '/student/consult/check',
  '/student/consult/todo',
  '/student/sale/todo',
  '/student/sale/list',
  '/student/graduate/todo',
  '/student/graduate/list',
  '/student/vip/list',
  '/student/vip/status',
  '/student/class-teacher/student-list',
  '/student/class-teacher/student-history',
  '/teaching_research/todo',
  '/teaching_research/list',
  '/teaching_research/zoom',
  '/teacher/todo',
  '/teacher/list',
];

function accessItem(data: any[], auth: any): any[] {
  return (
    data?.flatMap((item) =>
      auth?.[item?.key]
        ? [
            {
              ...item,
              ...(item?.children
                ? { children: accessItem(item?.children, auth) }
                : {}),
            },
          ]
        : [],
    ) || []
  );
}

const BasicLayout: React.FC<BasicLayoutProps> = (props) => {
  // state
  const { userInfo, logout } = useUserInfo({ manual: false });
  const navigate = useNavigate();
  const location = useLocation();
  const access = useAccess();
  const fixItems = accessItem(items, access.showPage);

  // action
  function handleLogout() {
    logout();
    window.location.reload();
  }

  // data
  const selectedKeys = itemKeys.filter((item) =>
    location?.pathname?.startsWith(item),
  );
  const defaultOpenKeys = itemOpenKeys.filter((item) =>
    location?.pathname?.startsWith(item),
  );

  return (
    <Layout className="h-screen">
      <Layout.Header className="flex justify-between bg-primary">
        <div className="text-white">新领域理工塾后台系统</div>
        <div className="text-white">
          {`管理员: ${userInfo.last_name_cn}${userInfo.first_name_cn}`}
        </div>
        <div>
          <Button onClick={handleLogout}>退出</Button>
        </div>
      </Layout.Header>
      <Layout>
        <Layout.Sider>
          <Menu
            mode="inline"
            selectedKeys={selectedKeys}
            defaultOpenKeys={defaultOpenKeys}
            className="h-full border-r-0"
            onSelect={({ key }) => navigate(key)}
            items={fixItems}
          />
        </Layout.Sider>
        <Layout.Content className="p-4">
          <Card
            className="h-full overflow-scroll"
            bodyStyle={{ height: '100%' }}
          >
            {props.children}
          </Card>
        </Layout.Content>
      </Layout>
    </Layout>
  );
};

export default BasicLayout;
