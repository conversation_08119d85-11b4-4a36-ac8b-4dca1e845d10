import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import { Form, Input, Table, Row, Col, Button, Space, Select } from 'antd';
////
import { filterOption, fixParams } from '@/utils/function';
import { renderDate, dayjs } from '@/hooks/useDayjs';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import { getAllStudents } from '@/services/request/student';
import { useUserInfo } from '@/hooks/useUserInfo';
import { useUserOptions } from '@/hooks/useApiOptions';
import { examDateCategory, goalLevelCategory, goalStatusCategory } from '@/utils/formConsts';
import ExportStudentsXlsx from '@/pages/student/components/ExportStudentsXlsx';

export interface StudentListProps {}

const StudentList: React.FC<StudentListProps> = () => {
  // state
  const [form] = Form.useForm();
  const { userInfo } = useUserInfo();
  const { userOptions, renderUserLabel } = useUserOptions();

  // Dynamic status calculation based on course_expired_date
  const renderDynamicStatus = (record: API.Student) => {
    const courseExpiredDate = (record as any)?.course_expired_date;
    const originalStatus = record?.on_status;

    if (courseExpiredDate) {
      const today = dayjs();
      const expiredDate = dayjs(courseExpiredDate);
      const daysUntilExpiry = expiredDate.diff(today, 'day');

      // If expired (past due)
      if (daysUntilExpiry < 0) {
        return '到期';
      }
      // If expiring within 30 days
      else if (daysUntilExpiry >= 0 && daysUntilExpiry <= 30) {
        return '即将到期';
      }
    }

    // Return original status for other cases
    return renderLabel(studentJSON.OnStatus)(originalStatus);
  };

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllStudents({
      on_status_in: JSON.stringify([2, 3, 4, 7]),
      ...fixParams(pageData, formData),
      teacher: userInfo?._id,
    });
    return {
      total: data?.totalCount,
      list: (data?.students as API.Student[]) || [],
    };
  };
  const studentsAPI = useAntdTable(getTableData, { form });

  return (
    <>
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="150px">
            <Form.Item name="on_status_in">
              <Select
                placeholder="在学状态"
                options={studentJSON.OnStatus}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="name_lk">
              <Input placeholder="学生姓名" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="wechat_lk">
              <Input placeholder="微信号" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="courses.0.department_name_lk">
              <Input placeholder="报名学院" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="goal_lists.0.hege_univ_lk">
              <Input placeholder="合格院校" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="goal_lists.0.level">
              <Select
                placeholder="评级"
                options={goalLevelCategory}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="goal_lists.0.exam_date">
              <Select
                placeholder="计划考试时间"
                options={examDateCategory}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="goal_lists.0.current_status">
              <Select
                placeholder="目前状态"
                options={goalStatusCategory}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="goal_lists.0.is_taoci_bool">
              <Select
                placeholder="套磁状态"
                options={studentJSON.Boolean}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
          <Form.Item name="current_follow_teacher">
            <Select
              allowClear
              showSearch
              placeholder="班主任"
              options={userOptions}
              filterOption={filterOption}
            />
          </Form.Item>
        </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={studentsAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={studentsAPI.search.reset}>重置</Button>
                <ExportStudentsXlsx
                  dataSource={studentsAPI.tableProps.dataSource}
                  teacherId={userInfo?._id?.toString()}
                  requiredRoles={['教务管理员', '班主任管理员', '学部班主任管理员', '大学院班主任管理员']}
                  buttonText="导出学生数据"
                  filename="班主任学生数据.xlsx"
                  exportType="teacher"
                />
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowKey="_id"
        size="small"
        {...studentsAPI.tableProps}
        pagination={{
          ...studentsAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ x: 2000 }}
      >
        <Table.Column
          fixed="left"
          width={120}
          title="课程有效期"
          dataIndex="course_expired_date"
          render={renderDate('YYYY-MM-DD')}
        />
        <Table.Column
          fixed="left"
          width={120}
          title="在学状态"
          dataIndex="on_status"
          render={(_, record: API.Student) => renderDynamicStatus(record)}
        />
        <Table.Column<API.Student>
          fixed="left"
          sorter
          width={120}
          title="学⽣姓名"
          dataIndex="name"
          render={(_, row) => (
            <Link to={`/student/${row?._id}`}>{row?.name}</Link>
          )}
        />
        <Table.Column title="微信号" dataIndex="wechat" />
        <Table.Column
          title="报名学院"
          dataIndex={['courses', 0, 'department_name']}
        />
        <Table.Column
          title="报名课程"
          dataIndex={['courses', 0, 'course_name']}
        />
        <Table.Column
          title="VIP课程"
          dataIndex={['courses', 0, 'vip_course']}
        />
        <Table.Column
          title="数学课程"
          dataIndex={['courses', 0, 'math_course_note']}
        />
        <Table.Column
          title="英语课程"
          dataIndex={['courses', 0, 'en_course_note']}
        />
        <Table.Column
          title="日语课程"
          dataIndex={['courses', 0, 'ja_course_note']}
        />
        <Table.Column
          title="合格院校"
          dataIndex={['goal_lists', 0, 'hege_univ']}
        />
        <Table.Column
          title="评级"
          dataIndex={['goal_lists', 0, 'level']}
          render={renderLabel(goalLevelCategory)}
        />
        <Table.Column
          title="计划考试时间"
          dataIndex={['goal_lists', 0, 'exam_date']}
          render={renderLabel(examDateCategory)}
        />
        <Table.Column
          title="目前状态"
          dataIndex={['goal_lists', 0, 'current_status']}
          render={renderLabel(goalStatusCategory)}
        />
        <Table.Column
          title="套磁状态"
          dataIndex={['goal_lists', 0, 'is_taoci']}
          render={renderLabel(studentJSON.Boolean)}
        />
        <Table.Column
          title="班主任"
          dataIndex="current_follow_teacher"
          render={renderUserLabel}
        />
      </Table>
    </>
  );
};

export default StudentList;
