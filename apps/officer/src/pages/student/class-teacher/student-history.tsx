import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import { Form, Input, Table, Row, Col, Button, Space, Select } from 'antd';
////
import { fixParams } from '@/utils/function';
import { renderDate } from '@/hooks/useDayjs';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import { getAllStudents } from '@/services/request/student';
import { useUserInfo } from '@/hooks/useUserInfo';
import { useUserOptions } from '@/hooks/useApiOptions';

export interface StudentHistoryProps {}

const StudentHistory: React.FC<StudentHistoryProps> = () => {
  // state
  const [form] = Form.useForm();
  const { userInfo } = useUserInfo();
  const { renderUserLabel } = useUserOptions();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllStudents({
      on_status_in: JSON.stringify([5, 6]),
      ...fixParams(pageData, formData),
      teacher: userInfo?._id,
    });
    return {
      total: data?.totalCount,
      list: (data?.students as API.Student[]) || [],
    };
  };
  const studentsAPI = useAntdTable(getTableData, { form });

  return (
    <>
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="150px">
            <Form.Item name="sign_status_in">
              <Select
                placeholder="签约状态"
                options={studentJSON.SignStatus}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="name_lk">
              <Input placeholder="学生姓名" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="wechat_lk">
              <Input placeholder="微信号" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="courses.0.department_name_lk">
              <Input placeholder="报名学院" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="pass_univ_lk">
              <Input placeholder="合格院校" />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={studentsAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={studentsAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowKey="_id"
        size="small"
        {...studentsAPI.tableProps}
        pagination={{
          ...studentsAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ x: 2000 }}
      >
        <Table.Column
          fixed="left"
          width={120}
          title="课程有效期"
          dataIndex="sign_expired_date"
          render={renderDate('YYYY-MM-DD')}
        />
        <Table.Column
          fixed="left"
          width={120}
          title="签约状态"
          dataIndex="sign_status"
          render={renderLabel(studentJSON.SignStatus)}
        />
        <Table.Column<API.Student>
          sorter
          fixed="left"
          width={120}
          title="学⽣姓名"
          dataIndex="name"
          render={(_, row) => (
            <Link to={`/student/${row?._id}`}>{row?.name}</Link>
          )}
        />
        <Table.Column title="学生姓名" dataIndex="name" />
        <Table.Column title="微信号" dataIndex="wechat" />
        <Table.Column
          title="报名学院"
          dataIndex={['courses', 0, 'department_name']}
        />
        <Table.Column
          title="报名课程"
          dataIndex={['courses', 0, 'course_name']}
        />
        <Table.Column
          title="VIP课程"
          dataIndex={['courses', 0, 'vip_course']}
        />
        <Table.Column
          title="数学课程"
          dataIndex={['courses', 0, 'math_course_note']}
        />
        <Table.Column
          title="英语课程"
          dataIndex={['courses', 0, 'en_course_note']}
        />
        <Table.Column
          title="日语课程"
          dataIndex={['courses', 0, 'ja_course_note']}
        />
        <Table.Column title="合格院校" dataIndex="pass_univ" />
        <Table.Column
          title="班主任"
          dataIndex="current_follow_teacher"
          render={renderUserLabel}
        />
      </Table>
    </>
  );
};

export default StudentHistory;
