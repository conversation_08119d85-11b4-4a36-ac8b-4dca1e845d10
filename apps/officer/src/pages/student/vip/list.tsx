import { useState } from 'react';
import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Form,
  message,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  Select,
  DatePicker,
} from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import type { TableRowSelection } from 'antd/es/table/interface';
////
import { filterOption, fixParams } from '@/utils/function';
import { useForm } from '@/hooks/useSKForm';
import { renderDate, renderHoursAndMinutes } from '@/hooks/useDayjs';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import { useStudentOptions, useUserOptions } from '@/hooks/useApiOptions';
import {
  createVipCourse,
  deleteVipCourseById,
  finishVipCourse,
  getAllVipCourses,
  updateVipCourse,
} from '@/services/request/vipCourse';
import StudentVipForm from '@/pages/student/components/StudentVipForm';

export interface VipListProps {}

const VipList: React.FC<VipListProps> = () => {
  // state
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [form] = Form.useForm();
  const { formType, formProps, handleOpen } = useForm<API.TODO>();
  const { userOptions, renderUserLabel } = useUserOptions();
  const { studentOptions, studentFind, renderStudentLabel } = useStudentOptions(
    {
      params: { is_vip_course: true },
      cacheKey: 'vipStudentOpts',
    },
  );

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllVipCourses({
      ...fixParams(pageData, formData),
    });
    return {
      total: data?.totalCount,
      list: (data?.vipCourses as API.VipCourse[]) || [],
    };
  };
  const vipCoursesAPI = useAntdTable(getTableData, { form });

  // format
  const rowSelection: TableRowSelection<API.VipCourse> = {
    type: 'checkbox',
    fixed: true,
    selectedRowKeys,
    getCheckboxProps: (row) => ({
      disabled: !row.student || !!row.status,
    }),
    onChange: (keys: any[]) => {
      setSelectedRowKeys(keys);
    },
  };
  const selected = selectedRowKeys?.length || 0;

  // action
  const handleClear = () => {
    setSelectedRowKeys([]);
  };
  const handleCheckStatus = async () => {
    try {
      await finishVipCourse({ ids: selectedRowKeys });
      handleClear()
      message.success("修改成功");
      vipCoursesAPI.search.submit();
    } catch (error: any) {
      handleClear()
      vipCoursesAPI.search.submit();
      message.error(error?.message || error);
    }
  };
  const handleAdd = () => {
    handleOpen({
      title: '新建VIP记录',
      type: 'add',
      data: null,
    });
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await createVipCourse({
          ...v,
          vip_date: v?.vip_date?.format('YYYY-MM-DD'),
        });
      }
      if (formType === 'edit') {
        await updateVipCourse({
          ...v,
          vipCourseId: formProps.dataSource?._id,
          vip_date: v?.vip_date?.format('YYYY-MM-DD'),
        });
      }
      vipCoursesAPI.search.submit();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };
  return (
    <>
      <StudentVipForm type={formType} {...formProps} onSubmit={handleSubmit} />
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="280px">
            <Form.Item name="vip_date_range">
              <DatePicker.RangePicker
                placeholder={['开始日期', '结束日期']}
                allowEmpty={[true, true]}
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="vip_teacher">
              <Select
                allowClear
                showSearch
                placeholder="讲师姓名"
                options={userOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="student">
              <Select
                allowClear
                showSearch
                placeholder="学生姓名"
                options={studentOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Col>
          <Col>
            <Space>
              <Button type="primary" onClick={vipCoursesAPI.search.submit}>
                搜索
              </Button>
              <Button onClick={vipCoursesAPI.search.reset}>重置</Button>
            </Space>
          </Col>
        </Row>
      </Form>
      <Button className="mb-4" type="primary" onClick={handleAdd}>
        新建
      </Button>
      <Row className="mb-2">
        <Col>
          <Space>
            <span>已选: {selected} 项</span>
            <Button size="small" type="link" onClick={handleClear}>
              清除已选
            </Button>
          </Space>
        </Col>
        <Col flex="auto"></Col>
        <Col>
          <Space>
            <span>批量操作：</span>
            <Button
              size="small"
              type="dashed"
              disabled={!selected}
              onClick={handleCheckStatus}
            >
              确认申报
            </Button>
          </Space>
        </Col>
      </Row>
      <Table
        rowKey="_id"
        size="small"
        {...vipCoursesAPI.tableProps}
        rowSelection={rowSelection}
        pagination={{
          ...vipCoursesAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
      >
        <Table.Column
          title="日期"
          dataIndex="vip_date"
          sorter
          render={renderDate('MM/DD')}
        />
        <Table.Column
          title="讲师姓名"
          sorter
          dataIndex="vip_teacher"
          render={renderUserLabel}
        />
        <Table.Column
          title="学生姓名"
          sorter
          dataIndex="student"
          render={(student) => (
            <Link to={`/student/${student}`}>
              {renderStudentLabel(student)}
            </Link>
          )}
        />
        <Table.Column title="开始时间" dataIndex="start_date_str" />
        <Table.Column title="结束时间" dataIndex="end_date_str" />
        <Table.Column
          title="讲义时间"
          dataIndex="duration"
          render={renderHoursAndMinutes()}
        />
        <Table.Column
          title="学生属性"
          dataIndex="student"
          render={(v) =>
            renderLabel(studentJSON.StudentType)(studentFind(v)?.student_type)
          }
        />
        <Table.Column title="备注" dataIndex="note" />
        <Table.Column
          title="年月"
          dataIndex="vip_date"
          render={renderDate('YYYYMM')}
        />
        <Table.Column
          title="删除"
          fixed="right"
          width={50}
          render={(row) => {
            const handleDelete = () => {
              Modal.confirm({
                centered: true,
                title: '删除',
                content: '确认删除本条申报，删除后无法恢复。',
                onOk: async () => {
                  await deleteVipCourseById({
                    vipCourseId: row?._id,
                  });
                  vipCoursesAPI.search.submit();
                },
                okText: '确认',
                cancelText: '取消',
              });
            };
            return (
              <Button
                size="small"
                onClick={handleDelete}
                disabled={[2, 4].includes(row?.apply_status) || !!row?.status}
              >
                <DeleteOutlined />
              </Button>
            );
          }}
        />
      </Table>
    </>
  );
};

export default VipList;
