import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  Table,
  Row,
  Col,
  Button,
  Space,
  Select,
  Badge,
} from 'antd';
////
import { fixParams } from '@/utils/function';
import { getAllStudents } from '@/services/request/student';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import { renderHours, renderHoursAndMinutes } from '@/hooks/useDayjs';
import { useUserInfo } from '@/hooks/useUserInfo';

export interface VipStatusProps {}

const VipStatus: React.FC<VipStatusProps> = () => {
  // state
  const [form] = Form.useForm();
  const { userInfo } = useUserInfo();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllStudents({
      ...fixParams(pageData, formData),
      is_vip_course: true,
      teacher: userInfo?._id,
    });
    return {
      total: data?.totalCount,
      list: (data?.students as API.VipCourse[]) || [],
    };
  };
  const studentsAPI = useAntdTable(getTableData, { form });

  return (
    <>
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="180px">
            <Form.Item name="student_type">
              <Select
                placeholder="学生状态"
                options={studentJSON.StudentType}
              />
            </Form.Item>
          </Col>
          <Col flex="180px">
            <Form.Item name="name_lk">
              <Input placeholder="学生姓名" />
            </Form.Item>
          </Col>
          <Col>
            <Space>
              <Button type="primary" onClick={studentsAPI.search.submit}>
                搜索
              </Button>
              <Button onClick={studentsAPI.search.reset}>重置</Button>
            </Space>
          </Col>
        </Row>
      </Form>
      <Table
        rowKey="_id"
        size="small"
        {...studentsAPI.tableProps}
        pagination={{
          ...studentsAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
      >
        <Table.Column<API.Student>
          title="学⽣姓名"
          dataIndex="name"
          render={(_, row) => (
            <Link to={`/student/${row?._id}`}>
              {(row?.vip_total_hours || 0) <= (row?.vip_used_hours || 0) && (
                <Badge status="error" />
              )}{' '}
              {row?.name}
            </Link>
          )}
        />
        <Table.Column
          title="学生属性"
          dataIndex="student_type"
          render={renderLabel(studentJSON.StudentType)}
        />
        <Table.Column
          title="VIP总时间"
          dataIndex="vip_total_hours"
          render={renderHoursAndMinutes()}
        />
        <Table.Column
          title="已消耗时间"
          dataIndex="vip_used_hours"
          render={renderHoursAndMinutes()}
        />
        <Table.Column
          title="剩余时间"
          render={(row) =>
            renderHoursAndMinutes()(
              (row?.vip_total_hours || 0) - (row?.vip_used_hours || 0),
            )
          }
        />
        <Table.Column
          title="消耗占比"
          render={(row) =>
            row?.vip_total_hours
              ? `${((row?.vip_used_hours * 100) / row?.vip_total_hours).toFixed(
                  2,
                )} %`
              : ''
          }
        />
      </Table>
    </>
  );
};

export default VipStatus;
