import { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Descriptions,
  Select,
  DatePicker,
  InputNumber,
} from 'antd';
////
import { initFormDate } from '@/hooks/useDayjs';
import { studentJSON } from '@/hooks/useJSON';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
import { examDateCategory, goalLevelCategory, goalStatusCategory } from '@/utils/formConsts';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.TODO {}

export interface GoalInfoFormProps extends SKFormProps<DataSource> {}

const GoalInfoForm: React.FC<GoalInfoFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue({
        ...props?.dataSource,
        date: initFormDate(props?.dataSource?.date),
      });
    }
  }, [props]);

  return (
    <Modal {...modalProps} width={900}>
      <Form
        name="GoalInfoForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
        layout="inline"
      >
        <Descriptions className="w-full" size="small" bordered column={1}>
          <Descriptions.Item label="评级">
            <Form.Item name="level">
              <Select options={goalLevelCategory} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="计划考试时间">
            <Form.Item name="exam_date">
              <Select options={examDateCategory} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="目前状态">
            <Form.Item name="current_status">
              <Select options={goalStatusCategory} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="套磁状态">
            <Form.Item name="is_taoci">
              <Select options={studentJSON.Boolean} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="套磁院校和教授">
            <Form.Item name="taoci_detail">
              <Input.TextArea />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="套磁结果" span={2}>
            <Form.Item name="taoci_result">
              <Input.TextArea />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="报考院校" span={2}>
            <Form.Item name="baokao_univ">
              <Input.TextArea />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="合格院校" span={2}>
            <Form.Item name="hege_univ">
              <Input.TextArea />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="合格经验" span={2}>
            <Form.Item name="hege_jingyan">
              <Input.TextArea />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="近况" span={2}>
            <Form.Item name="jinkuang">
              <Input.TextArea />
            </Form.Item>
          </Descriptions.Item>
        </Descriptions>
      </Form>
    </Modal>
  );
};

export default GoalInfoForm;
