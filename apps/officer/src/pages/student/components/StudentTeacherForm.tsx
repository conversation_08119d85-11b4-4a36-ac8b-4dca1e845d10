import { useEffect } from 'react';
import { Modal, Form, Select } from 'antd';
////
import { filterOption } from '@/utils/function';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
import { useUserOptions } from '@/hooks/useApiOptions';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.TODO {}

export interface StudentTeacherFormProps extends SKFormProps<DataSource> {
  options?: {};
}

const StudentTeacherForm: React.FC<StudentTeacherFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);
  const { userOptions } = useUserOptions();

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue({
        current_follow_teacher: props?.dataSource?.current_follow_teacher,
      });
    }
  }, [props]);

  return (
    <Modal {...modalProps}>
      <Form
        name="StudentTeacherForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
      >
        <Form.Item label="班主任" name="current_follow_teacher">
          <Select
            allowClear
            showSearch
            options={userOptions}
            filterOption={filterOption}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default StudentTeacherForm;
