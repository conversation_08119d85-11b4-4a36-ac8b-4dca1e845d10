import { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Descriptions,
  Select,
  DatePicker,
  InputNumber,
} from 'antd';
////
import { initFormDate } from '@/hooks/useDayjs';
import { studentJSON } from '@/hooks/useJSON';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.TODO {}

export interface StudentPaymentFormProps extends SKFormProps<DataSource> {}

const StudentPaymentForm: React.FC<StudentPaymentFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue({
        ...props?.dataSource,
        date: initFormDate(props?.dataSource?.date),
      });
    }
  }, [props]);

  return (
    <Modal {...modalProps} width={900}>
      <Form
        name="StudentPaymentForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
        layout="inline"
      >
        <Descriptions className="w-full" size="small" bordered column={1}>
          <Descriptions.Item label="付款形式">
            <Form.Item name="type">
              <Select options={studentJSON.paymentHistoryType} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="付款金额">
            <Form.Item name="amount">
              <InputNumber style={{ width: 200 }} min={0} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="付款方式">
            <Form.Item name="method">
              <Select options={studentJSON.paymentHistoryMethod} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="付款完成日">
            <Form.Item name="date">
              <DatePicker style={{ width: 200 }} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="备注">
            <Form.Item name="note">
              <Input.TextArea />
            </Form.Item>
          </Descriptions.Item>
        </Descriptions>
      </Form>
    </Modal>
  );
};

export default StudentPaymentForm;
