import { useEffect } from 'react';
import { Modal, Form, Input, Descriptions } from 'antd';
////
import { dayjs } from '@/hooks/useDayjs';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
import S3Upload from './S3Upload';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.TODO {}

export interface StudentFollowFormProps extends SKFormProps<DataSource> {}

const StudentFollowForm: React.FC<StudentFollowFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue(props?.dataSource);
    }
  }, [props]);

  return (
    <Modal {...modalProps} width={900}>
      <Form
        name="StudentFollowForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
        layout="inline"
      >
        <Descriptions className="w-full" size="small" bordered column={1}>
          <Descriptions.Item label="跟进类型">
            <Form.Item name="category">
              <Input />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="跟进内容">
            <Form.Item name="content">
              <Input />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="跟进附件">
            <Form.Item
              name="files"
              valuePropName="fileList"
              getValueFromEvent={(e) => {
                if (Array.isArray(e)) {
                  return e;
                }
                return e?.fileList;
              }}
            >
              <S3Upload
                maxCount={3}
                pathName={'payment_images/' + dayjs().format('YYYYMMDD')}
              />
            </Form.Item>
          </Descriptions.Item>
        </Descriptions>
      </Form>
    </Modal>
  );
};

export default StudentFollowForm;
