import { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Descriptions,
  DatePicker,
  Select,
  Space,
  Button,
  InputNumber,
} from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
////
import { dayjs, initFormDate } from '@/hooks/useDayjs';
import { studentJSON } from '@/hooks/useJSON';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
import { filterOption } from '@/utils/function';
import { useUserOptions } from '@/hooks/useApiOptions';
import { useAccess } from '@/hooks/useAccess';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.Student {}

export interface StudentInfoSimpleFormProps extends SKFormProps<DataSource> {
  options?: {};
}

const StudentInfoSimpleForm: React.FC<StudentInfoSimpleFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);
  const { userOptions } = useUserOptions();
  const access = useAccess();

  const canEditAll = access.page?.['studentInfo.D'];

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue({
        ...props?.dataSource,
        to_jp_date: initFormDate(props?.dataSource?.to_jp_date),
      });
    }
  }, [props]);

  return (
    <Modal {...modalProps} width={900}>
      <Form
        name="StudentInfoSimpleForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
        layout="inline"
        className="descriptions-form"
      >
        {(props.type === 'editStudentInfo') && (
          <Descriptions
            className="w-full"
            size="small"
            title="个人信息2"
            bordered
            column={2}
          >
            <Descriptions.Item label="在留卡号" span={2}>
              <Form.Item name="JP_ID_no">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="二代居民身份证" span={2}>
              <Form.Item name="CN_ID_no">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="手机号码（日本）" span={2}>
              <Form.Item name="tel_jp">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="手机号码（中国）" span={2}>
              <Form.Item name="tel_cn">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="紧急联系人姓名">
              <Form.Item name="emergency_name">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="与紧急联系人的关系">
              <Form.Item name="emergency_relationship">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="紧急联系人手机号">
              <Form.Item name="emergency_tel">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="紧急联系人微信号">
              <Form.Item name="emergency_wechat">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="住址" span={2}>
              <Form.Item name="address">
                <Input />
              </Form.Item>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Form>
    </Modal>
  );
};

export default StudentInfoSimpleForm;
