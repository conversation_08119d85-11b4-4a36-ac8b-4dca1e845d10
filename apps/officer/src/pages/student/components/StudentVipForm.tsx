import { useEffect } from 'react';
import { Modal, Form, Input, Select, Descriptions, DatePicker } from 'antd';
////
import { filterOption } from '@/utils/function';
import { timesJSON } from '@/hooks/useJSON';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
import { dayjs, initFormDate } from '@/hooks/useDayjs';
import { useStudentOptions, useUserOptions } from '@/hooks/useApiOptions';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.TODO {}

export interface StudentVipFormProps extends SKFormProps<DataSource> {
  options?: {};
}

const StudentVipForm: React.FC<StudentVipFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);
  const { userOptions } = useUserOptions();
  const { studentOptions } = useStudentOptions({
    params: { is_vip_course: true },
    cacheKey: 'vipStudentOpts',
  });

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue({
        ...props?.dataSource,
        vip_date: initFormDate(props?.dataSource?.vip_date),
      });
    }
  }, [props]);

  // action
  const handleSumChange = () => {
    const { start_date_str, end_date_str } = formProps.form.getFieldsValue();
    let duration = 0;
    if (start_date_str && end_date_str) {
      const [sh, smm] = start_date_str?.split(':');
      const start_time = dayjs()
        ?.set('hour', +sh)
        ?.set('minute', +smm)
        ?.set('second', 0);
      const [eh, emm] = end_date_str?.split(':');
      const end_time = dayjs()
        ?.set('hour', +eh)
        ?.set('minute', +emm)
        ?.set('second', 0);
      duration =
        end_time
          ?.startOf('minute')
          ?.diff(start_time?.startOf('minute'), 'minute') || 0;
      duration = duration / 60;
    }
    formProps.form.setFieldsValue({ duration });
  };

  return (
    <Modal {...modalProps} width={1000}>
      <Form
        name="StudentVipForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
        layout="inline"
      >
        <Descriptions className="w-full" size="small" title="个人信息" bordered>
          <Descriptions.Item label="上课日期（必填）" span={3}>
            <Form.Item name="vip_date" rules={[{ required: true }]}>
              <DatePicker />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="开始时间（必填）">
            <Form.Item name="start_date_str" rules={[{ required: true }]}>
              <Select
                showSearch
                options={timesJSON}
                style={{ width: 140 }}
                onChange={handleSumChange}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="结束时间（必填）">
            <Form.Item name="end_date_str" rules={[{ required: true }]}>
              <Select
                showSearch
                options={timesJSON}
                style={{ width: 140 }}
                onChange={handleSumChange}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="总时长">
            <Form.Item name="duration">
              <Input disabled style={{ width: 140 }} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="学生姓名" span={3}>
            <Form.Item name="student">
              <Select
                allowClear
                showSearch
                options={studentOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="讲师姓名（必填）" span={3}>
            <Form.Item name="vip_teacher">
              <Select
                allowClear
                showSearch
                options={userOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="备注（工作内容）" span={3}>
            <Form.Item name="note">
              <Input />
            </Form.Item>
          </Descriptions.Item>
        </Descriptions>
      </Form>
    </Modal>
  );
};

export default StudentVipForm;
