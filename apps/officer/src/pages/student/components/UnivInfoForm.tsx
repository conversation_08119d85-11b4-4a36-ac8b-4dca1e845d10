import { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Descriptions,
  DatePicker,
  Select,
  Space,
  Button,
  InputNumber,
} from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
////
import { dayjs, initFormDate } from '@/hooks/useDayjs';
import { studentJSON } from '@/hooks/useJSON';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
import { filterOption } from '@/utils/function';
import { useUserOptions } from '@/hooks/useApiOptions';
import { useAccess } from '@/hooks/useAccess';
import { GoalFacultyCategory, UnivStatusCategory } from '@/utils/formConsts';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.Student {}

export interface UnivInfoFormProps extends SKFormProps<DataSource> {
  options?: {};
}

const UnivInfoForm: React.FC<UnivInfoFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);
  const { userOptions } = useUserOptions();

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue({
        ...props?.dataSource,
        to_jp_date: initFormDate(props?.dataSource?.to_jp_date),
      });
    }
  }, [props]);

  return (
    <Modal {...modalProps} width={900}>
      <Form
        name="UnivInfoForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
        layout="inline"
        className="descriptions-form"
      >
        {props.type === 'common' && (
          <Descriptions
            className="w-full"
            size="small"
            title=" "
            bordered
            column={2}
          >
            <Descriptions.Item label="在学状态" span={2}>
              <Form.Item name="on_status">
                <Select options={studentJSON.OnStatus} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="分期付款已完成" span={2}>
              <Form.Item name="is_paid">
                <Select options={studentJSON.IsPaid} allowClear />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="班主任" span={2}>
              <Form.Item name="current_follow_teacher">
                <Select
                  allowClear
                  showSearch
                  options={userOptions}
                  filterOption={filterOption}
                />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="有无VIP课程">
              <Form.Item name="is_vip_course">
                <Select options={studentJSON.Boolean} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="VIP课程时长">
              <Form.Item name="vip_total_hours">
                <InputNumber min={0} />
              </Form.Item>
            </Descriptions.Item>
          </Descriptions>
        )}
        {props.type === 'editUnivInfo' && (
          <Descriptions
            className="w-full"
            size="small"
            title="考学信息"
            bordered
            column={2}
          >
            <Descriptions.Item label="销售老师" span={2}>
              <Form.Item name="sale_teacher1">
                <Select
                  showSearch
                  options={userOptions}
                  filterOption={filterOption}
                />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="学生属性" span={2}>
              <Form.Item name="student_type">
                <Select options={studentJSON.StudentType} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="学生姓名（必填）">
              <Form.Item name="name" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="微信号">
              <Form.Item name="wechat">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="学生来源" span={2}>
              <Form.Item name="source_from">
                <Input disabled={props.type === 'editUnivInfo'} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="毕业院校类别">
              <Form.Item name="grad_univ_category">
                <Select options={studentJSON.GradUnivCategory} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="毕业院校">
              <Form.Item name="grad_univ">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="专业">
              <Form.Item name="grad_faculty">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="现在状态">
              <Form.Item name="current_univ_status">
                <Select options={UnivStatusCategory} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="志望院校">
              <Form.Item name="goal_univ">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="志望专业">
              <Form.Item name="goal_faculty">
                <Select options={GoalFacultyCategory} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="有无来日本的日期">
              <Form.Item name="is_to_jp">
                <Select options={studentJSON.Boolean} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="来日本的日期">
              <Form.Item name="to_jp_date">
                <DatePicker picker="month" />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="有无语言学校">
              <Form.Item name="is_lang_school">
                <Select options={studentJSON.Boolean} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="语言学校">
              <Form.Item name="lang_school">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="英语成绩" span={2}>
              <Form.List name="en_scores">
                {(fields, { add, remove }) => (
                  <Space direction="vertical">
                    {fields.map(({ key, ...field }) => (
                      <Space key={key}>
                        <Form.Item {...field} name={[field.name, 'category']}>
                          <Select
                            placeholder="英语成绩种类"
                            options={studentJSON.EnScores}
                            style={{ width: 210 }}
                          />
                        </Form.Item>
                        <Form.Item {...field} name={[field.name, 'score']}>
                          <Input
                            placeholder="英语成绩"
                            style={{ width: 300 }}
                          />
                        </Form.Item>
                        <MinusCircleOutlined
                          onClick={() => remove(field.name)}
                        />
                      </Space>
                    ))}
                    <Button
                      style={{ width: 572 }}
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                    >
                      添加英语成绩
                    </Button>
                  </Space>
                )}
              </Form.List>
            </Descriptions.Item>
            <Descriptions.Item label="日语成绩" span={2}>
              <Form.List name="ja_scores">
                {(fields, { add, remove }) => (
                  <Space direction="vertical">
                    {fields.map(({ key, ...field }) => (
                      <Space key={key}>
                        <Form.Item {...field} name={[field.name, 'category']}>
                          <Select
                            placeholder="日语成绩种类"
                            options={studentJSON.JaScores}
                            style={{ width: 210 }}
                          />
                        </Form.Item>
                        <Form.Item {...field} name={[field.name, 'score']}>
                          <Input
                            placeholder="日语成绩"
                            style={{ width: 300 }}
                          />
                        </Form.Item>
                        <MinusCircleOutlined
                          onClick={() => remove(field.name)}
                        />
                      </Space>
                    ))}
                    <Button
                      style={{ width: 572 }}
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                    >
                      添加日语成绩
                    </Button>
                  </Space>
                )}
              </Form.List>
            </Descriptions.Item>
            <Descriptions.Item label="咨询备注" span={2}>
              <Form.Item name="consult_note">
                <Input.TextArea />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="契约条款备注" span={2}>
              <Form.Item name="contract_note">
                <Input.TextArea />
              </Form.Item>
            </Descriptions.Item>
          </Descriptions>
        )}
        {/* {props.type === 'edit' && !canEditAll && (
          <Descriptions
            className="w-full"
            size="small"
            title="身份信息"
            bordered
            column={2}
          >
            <Descriptions.Item label="销售老师" span={2}>
              <Form.Item name="sale_teacher1">
                <Select
                  showSearch
                  options={userOptions}
                  filterOption={filterOption}
                />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="学生属性" span={2}>
              <Form.Item name="student_type">
                <Select options={studentJSON.StudentType} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="学生姓名（必填）">
              <Form.Item name="name" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="微信号">
              <Form.Item name="wechat">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="毕业院校类别">
              <Form.Item name="grad_univ_category">
                <Select options={studentJSON.GradUnivCategory} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="毕业院校">
              <Form.Item name="grad_univ">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="志望院校">
              <Form.Item name="goal_univ">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="志望专业">
              <Form.Item name="goal_faculty">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="有无来日本的日期">
              <Form.Item name="is_to_jp">
                <Select options={studentJSON.Boolean} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="来日本的日期">
              <Form.Item name="to_jp_date">
                <DatePicker picker="month" />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="有无语言学校">
              <Form.Item name="is_lang_school">
                <Select options={studentJSON.Boolean} />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="语言学校">
              <Form.Item name="lang_school">
                <Input />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="英语成绩" span={2}>
              <Form.List name="en_scores">
                {(fields, { add, remove }) => (
                  <Space direction="vertical">
                    {fields.map(({ key, ...field }) => (
                      <Space key={key}>
                        <Form.Item {...field} name={[field.name, 'category']}>
                          <Select
                            placeholder="英语成绩种类"
                            options={studentJSON.EnScores}
                            style={{ width: 210 }}
                          />
                        </Form.Item>
                        <Form.Item {...field} name={[field.name, 'score']}>
                          <Input
                            placeholder="英语成绩"
                            style={{ width: 300 }}
                          />
                        </Form.Item>
                        <MinusCircleOutlined
                          onClick={() => remove(field.name)}
                        />
                      </Space>
                    ))}
                    <Button
                      style={{ width: 572 }}
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                    >
                      添加英语成绩
                    </Button>
                  </Space>
                )}
              </Form.List>
            </Descriptions.Item>
            <Descriptions.Item label="日语成绩" span={2}>
              <Form.List name="ja_scores">
                {(fields, { add, remove }) => (
                  <Space direction="vertical">
                    {fields.map(({ key, ...field }) => (
                      <Space key={key}>
                        <Form.Item {...field} name={[field.name, 'category']}>
                          <Select
                            placeholder="日语成绩种类"
                            options={studentJSON.JaScores}
                            style={{ width: 210 }}
                          />
                        </Form.Item>
                        <Form.Item {...field} name={[field.name, 'score']}>
                          <Input
                            placeholder="日语成绩"
                            style={{ width: 300 }}
                          />
                        </Form.Item>
                        <MinusCircleOutlined
                          onClick={() => remove(field.name)}
                        />
                      </Space>
                    ))}
                    <Button
                      style={{ width: 572 }}
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                    >
                      添加日语成绩
                    </Button>
                  </Space>
                )}
              </Form.List>
            </Descriptions.Item>
            <Descriptions.Item label="咨询备注" span={2}>
              <Form.Item name="consult_note">
                <Input.TextArea />
              </Form.Item>
            </Descriptions.Item>
            <Descriptions.Item label="契约条款备注" span={2}>
              <Form.Item name="contract_note">
                <Input.TextArea />
              </Form.Item>
            </Descriptions.Item>
          </Descriptions>
        )} */}
      </Form>
    </Modal>
  );
};

export default UnivInfoForm;
