import * as XLSX from 'xlsx';
import { Button } from 'antd';
////
import { renderDate } from '@/hooks/useDayjs';
import { studentJSON } from '@/hooks/useJSON';
import { renderLabel, useUserOptions } from '@/hooks/useApiOptions';
import { getAllStudents } from '@/services/request/student';
import { useUserInfo } from '@/hooks/useUserInfo';

export interface ExportStudentsXlsxProps {
  dataSource: any[];
  teacherId?: string; // Optional teacher ID for filtering
  requiredRoles?: string[]; // Optional roles required to show the button
  buttonText?: string; // Optional custom button text
  filename?: string; // Optional custom filename
  exportType?: 'all' | 'teacher'; // Type of export for different data structures
}

const ExportStudentsXlsx: React.FC<ExportStudentsXlsxProps> = (props) => {
  const {
    teacherId,
    requiredRoles,
    buttonText = '导出学生数据',
    filename = '学生数据.xlsx',
    exportType = 'all'
  } = props;

  const { userInfo } = useUserInfo();
  const { userFind } = useUserOptions();

  // Check if user has required permissions
  const hasPermission = () => {
    if (!requiredRoles || requiredRoles.length === 0) return true;

    const roles = userInfo?.roles || [];
    const isSuper = userInfo?.role === 0;
    return isSuper || roles.some((r) => requiredRoles.includes(r));
  };

  // api
  const getTableData = async () => {
    const params: any = {
      page: 0,
      perPage: 99999
    };

    // Add teacher filtering if teacherId is provided
    if (teacherId) {
      params.teacher = teacherId;
      params.on_status_in = JSON.stringify([2, 3, 4, 7]);
    }

    const data = await getAllStudents(params);
    return {
      total: data?.totalCount,
      list: (data?.students as API.Student[]) || [],
    };
  };

  const handleExport = async () => {
    // 修正数据格式
    // const totalRecords = props?.dataSource?.reduce((res: any, item: any) => {
    //   const userData = userOptions;
    //   let temp = {
    //     sale_teacher1: userFind(res?.sale_teacher1)?.value,
    //   };
    //   // for (const key in item?.amounts) {
    //   //   if (Object.prototype.hasOwnProperty.call(item?.amounts, key)) {
    //   //     const element = item?.amounts[key];
    //   //     temp.amounts[key] = (temp.amounts[key] || 0) + element;
    //   //   }
    //   // }
    //   return temp;
    // }, {});

    // const data = [...(props?.dataSource || []), totalRecords];
    const tableData = await getTableData();
    const data = tableData.list || [];

    // Create export data structure based on export type
    const fixExportData = data?.map((item) => {
      const baseData = {
        姓名: item?.name,
        创建时间: renderDate('YYYY-MM-DD')(item?.createdAt),
        微信号: item?.wechat,
        班主任: userFind(item?.current_follow_teacher)?.label,
      };

      return {
        ...baseData,
        学生属性: renderLabel(studentJSON.StudentType)(item?.student_type),
        销售老师: userFind(item?.sale_teacher1)?.label,
        毕业院校类别: item?.grad_univ_category,
        毕业院校: item?.grad_univ,
        专业: item?.grad_faculty,
        现在状态: item?.current_univ_status,
        志望院校: item?.goal_univ,
        志望专业: item?.goal_faculty,
        来日本的日期: renderDate('YYYY-MM')(item?.to_jp_date),
        语言学校: item?.lang_school,
        英语成绩: (item?.en_scores?.map((i: any) => (
          `${i?.category}: ${i?.score}`
        ))).join('/'),
        日语成绩: (item?.ja_scores?.map((i: any) => (
          `${i?.category}: ${i?.score}`
        ))).join('/'),
        咨询备注: item?.consult_note,
        来源: item?.source_from,
        在留卡号: item?.JP_ID_no,
        身份证号: item?.CN_ID_no,
        "手机号码（日本）": item?.tel_jp,
        "手机号码（中国）": item?.tel_cn,
        住址: item?.address,
        紧急联系人姓名: item?.emergency_name,
        与紧急联系人的关系: item?.emergency_relationship,
        紧急联系人手机号: item?.emergency_tel,
        紧急联系人微信号: item?.emergency_wechat,
        评级: item?.goal_lists?.[0]?.level,
        计划考试时间: item?.goal_lists?.[0]?.exam_date,
        目前状态: item?.goal_lists?.[0]?.current_status,
        套磁状态: item?.goal_lists?.[0]?.is_taoci ? '是' : '否',
        套磁院校和教授: item?.goal_lists?.[0]?.taoci_detail,
        套磁结果: item?.goal_lists?.[0]?.taoci_result,
        报考院校: item?.goal_lists?.[0]?.baokao_univ,
        合格院校: item?.goal_lists?.[0]?.hege_univ,
        合格经验: item?.goal_lists?.[0]?.hege_jingyan,
        近况: item?.goal_lists?.[0]?.jinkuang,
      };

    });

    let wb = XLSX.utils.book_new();
    let ws = XLSX.utils.json_to_sheet(fixExportData, { skipHeader: false });
    XLSX.utils.book_append_sheet(wb, ws, exportType === 'teacher' ? '班主任学生数据' : 'MIC');

    if (fixExportData.length > 0) {
      ws['!cols'] = Array.from(Object.keys(fixExportData[0]), () => ({
        wch: 20,
      }));
    }

    const s2ab = (s: any) => {
      // 字符串转字符流
      let buf = new ArrayBuffer(s.length);
      let view = new Uint8Array(buf);
      for (let i = 0; i !== s.length; ++i) {
        view[i] = s.charCodeAt(i) & 0xff;
      }
      return buf;
    };
    // 创建二进制对象写入转换好的字节流
    let tmpDown = new Blob(
      [
        s2ab(
          XLSX.write(wb, {
            bookType: 'xlsx',
            bookSST: false,
            type: 'binary',
          }),
        ),
      ],
      { type: '' },
    );
    let a = document.createElement('a');
    // 利用URL.createObjectURL()方法为a元素生成blob URL
    a.href = URL.createObjectURL(tmpDown); // 创建对象超链接
    a.download = filename;
    a.click();
  };

  // Only render button if user has permission
  if (!hasPermission()) {
    return null;
  }

  return (
    <Button size="middle" onClick={handleExport}>
      {buttonText}
    </Button>
  );
};

export default ExportStudentsXlsx;
