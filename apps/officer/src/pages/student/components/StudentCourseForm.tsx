import { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Descriptions,
  Select,
  InputNumber,
  DatePicker,
} from 'antd';
////
import { initFormDate } from '@/hooks/useDayjs';
import { studentJSON } from '@/hooks/useJSON';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
import { useUserOptions } from '@/hooks/useApiOptions';
import { filterOption } from '@/utils/function';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};
const formatterPrice = (value: any) =>
  `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

interface DataSource extends API.TODO {}

export interface StudentAppFormProps extends SKFormProps<DataSource> {}

const StudentAppForm: React.FC<StudentAppFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);
  const { userOptions } = useUserOptions();

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue({
        ...props?.dataSource,
        expired_date: initFormDate(props?.dataSource?.expired_date),
        sign_date: initFormDate(props?.dataSource?.sign_date),
      });
      if(props?.type === 'add') {
        formProps?.form?.setFieldsValue({
          custom_source: props?.dataSource?.source_from
        })
      }
    }
  }, [props]);

  // action
  const handleChangePrice = () => {
    const original_price =
      formProps?.form?.getFieldValue('original_price') || 0;
    const off_price = formProps?.form?.getFieldValue('off_price') || 0;
    const final_price = original_price - off_price;
    formProps?.form?.setFieldValue(
      'final_price',
      final_price < 0 ? 0 : final_price,
    );
  };

  return (
    <Modal {...modalProps} width={900}>
      <Form
        name="StudentAppForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
        layout="inline"
      >
        <Descriptions className="w-full" size="small" bordered column={2}>
          <Descriptions.Item label="经办人" span={2}>
            <Form.Item name="accept_teacher">
              <Select
                showSearch
                options={userOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="报名院校（必填）">
            <Form.Item name="department_name" rules={[{ required: true }]}>
              <Select
                style={{ width: 120 }}
                options={studentJSON.DepartmentName}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="报名课程">
            <Form.Item name="course_name">
              <Input style={{ width: 360 }} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="vip课程" span={2}>
            <Form.Item name="vip_course">
              <Input />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="数学课程">
            <Form.Item name="is_math_course">
              <Select style={{ width: 120 }} options={studentJSON.Boolean} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="数学课程备注">
            <Form.Item name="math_course_note">
              <Input style={{ width: 360 }} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="英语课程">
            <Form.Item name="is_en_course">
              <Select style={{ width: 120 }} options={studentJSON.Boolean} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="英语课程备注">
            <Form.Item name="en_course_note">
              <Input style={{ width: 360 }} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="日语课程">
            <Form.Item name="is_ja_course">
              <Select style={{ width: 120 }} options={studentJSON.Boolean} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="日语课程备注">
            <Form.Item name="ja_course_note">
              <Input style={{ width: 360 }} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="课程金额" span={2}>
            <Form.Item name="original_price">
              <InputNumber
                min={0}
                addonAfter="円"
                formatter={formatterPrice}
                onChange={handleChangePrice}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="优惠金额" span={2}>
            <Form.Item name="off_price">
              <InputNumber
                min={0}
                addonAfter="円"
                formatter={formatterPrice}
                onChange={handleChangePrice}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="实缴金额" span={2}>
            <Form.Item name="final_price">
              <InputNumber
                disabled
                addonAfter="円"
                formatter={formatterPrice}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="课程有效期" span={2}>
            <Form.Item name="expired_date">
              <DatePicker format="YYYY.MM.DD" />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="学生来源" span={2}>
            <Form.Item name="custom_source">
              <Input disabled/>
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="其他备注" span={2}>
            <Form.Item name="other_note">
              <Input.TextArea />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="签约日期" span={2}>
            <Form.Item name="sign_date">
              <DatePicker format="YYYY.MM.DD" />
            </Form.Item>
          </Descriptions.Item>
        </Descriptions>
      </Form>
    </Modal>
  );
};

export default StudentAppForm;
