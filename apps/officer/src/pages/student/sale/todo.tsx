import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  message,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  DatePicker,
  Select,
} from 'antd';
import { EditOutlined, DeleteOutlined, SendOutlined } from '@ant-design/icons';
////
import { fixParams, filterOption } from '@/utils/function';
import { useForm } from '@/hooks/useSKForm';
import { useUserInfo } from '@/hooks/useUserInfo';
import { renderDate } from '@/hooks/useDayjs';
import { useUserOptions } from '@/hooks/useApiOptions';
import {
  createStudent,
  deleteStudentById,
  getAllStudents,
  updateStudent,
} from '@/services/request/student';
import StudentForm from '@/pages/student/components/StudentInfoForm';

export interface GraduateTodoProps {}

const GraduateTodo: React.FC<GraduateTodoProps> = () => {
  // state
  const [form] = Form.useForm();
  const { formType, formProps, handleOpen } = useForm<API.Student>();
  const { userInfo } = useUserInfo();
  const { userOptions, renderUserLabel } = useUserOptions();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllStudents({
      sign_status_in: JSON.stringify([1,5]),
      ...fixParams(pageData, formData),
      teacher: userInfo?._id,
    });
    return {
      total: data?.totalCount,
      list: (data?.students as API.Student[]) || [],
    };
  };
  const studentsAPI = useAntdTable(getTableData, { form });

  // action
  const handleAdd = () => {
    handleOpen({
      title: '新建学生',
      type: 'add',
      data: null,
    });
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await createStudent({ ...v });
      }
      if (formType === 'edit') {
        await updateStudent({ ...v, studentId: formProps?.dataSource?._id });
      }
      studentsAPI.search.submit();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  return (
    <>
      <StudentForm type={formType} {...formProps} onSubmit={handleSubmit} />
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="280px">
            <Form.Item name="createdAt_range">
              <DatePicker.RangePicker
                placeholder={['创建时间', '创建时间']}
                allowEmpty={[true, true]}
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="name_lk">
              <Input placeholder="学生姓名" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="wechat_lk">
              <Input placeholder="微信号" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="courses.0.department_name_lk">
              <Input placeholder="报名学院" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="courses.0.course_name_lk">
              <Input placeholder="报名课程" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="courses.0.custom_source_lk">
              <Input placeholder="学生来源" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="sale_teacher1">
              <Select
                allowClear
                showSearch
                placeholder="销售老师"
                options={userOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={studentsAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={studentsAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowKey="_id"
        size="small"
        {...studentsAPI.tableProps}
        pagination={{
          ...studentsAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ x: 2300 }}
      >
        <Table.Column
          fixed="left"
          title="创建时间"
          width={180}
          dataIndex="createdAt"
          render={renderDate('YYYY.MM.DD HH:mm')}
        />
        <Table.Column<API.Student>
          sorter
          fixed="left"
          width={120}
          title="学⽣姓名"
          dataIndex="name"
          render={(_, row) => (
            <Link to={`/student/${row?._id}`}>{row?.name}</Link>
          )}
        />
        <Table.Column title="微信号" dataIndex="wechat" />
        <Table.Column
          title="报名学院"
          dataIndex={['courses', 0, 'department_name']}
        />
        <Table.Column
          title="报名课程"
          dataIndex={['courses', 0, 'course_name']}
        />
        <Table.Column
          title="课程金额"
          dataIndex={['courses', 0, 'original_price']}
        />
        <Table.Column
          title="优惠金额"
          dataIndex={['courses', 0, 'off_price']}
        />
        <Table.Column
          title="实际缴费金额"
          dataIndex={['courses', 0, 'final_price']}
        />
        <Table.Column
          title="学生来源"
          dataIndex={['courses', 0, 'custom_source']}
        />
        <Table.Column
          title="销售老师"
          dataIndex="sale_teacher1"
          render={renderUserLabel}
        />
        <Table.ColumnGroup title="操作">
          <Table.Column
            title="提交"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDelete = () => {
                Modal.confirm({
                  centered: true,
                  title: '提交给事务老师',
                  content: '提交后，本学生进入签约阶段，将无法删除。',
                  onOk: async () => {
                    await updateStudent({
                      studentId: row?._id,
                      sign_status: 2,
                      on_status: 1,
                    });
                    studentsAPI.search.submit();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button size="small" onClick={handleDelete}>
                  <SendOutlined />
                </Button>
              );
            }}
          />
          {/* <Table.Column
            title="编辑"
            fixed="right"
            width={50}
            render={(row) => {
              const handleEdit = () => {
                handleOpen({
                  title: '编辑学生基础信息',
                  type: 'edit',
                  data: row,
                });
              };
              return (
                <Button size="small" onClick={handleEdit}>
                  <EditOutlined />
                </Button>
              );
            }}
          /> */}
          <Table.Column
            title="删除"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDelete = () => {
                Modal.confirm({
                  centered: true,
                  title: '删除',
                  content: '确认删除本学生，删除后无法恢复。',
                  onOk: async () => {
                    await deleteStudentById({
                      studentId: row?._id,
                    });
                    studentsAPI.search.submit();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button size="small" onClick={handleDelete}>
                  <DeleteOutlined />
                </Button>
              );
            }}
          />
        </Table.ColumnGroup>
      </Table>
    </>
  );
};

export default GraduateTodo;
