import { useBoolean, useRequest } from 'ahooks';
import { Button, Modal, message } from 'antd';
import dayjs from 'dayjs';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
////
import { useUserOptions } from '@/hooks/useApiOptions';
import { genContractNo } from '@/services/request/student';
import { useParams } from 'umi';
import { formatterEn } from '@/utils/function';

export interface RenderJointProps {
  dataSource: {
    row: API.Course;
    data: API.Student;
  };
}

const renderData = (data: any) => (
  <span className="underline">{`\u2002\u2002\u2002\u2002${data || ''
    }\u2002\u2002\u2002\u2002`}</span>
);

const renderStrong = (data: any) => <span className="font-bold">{data}</span>;

const RenderJoint: React.FC<RenderJointProps> = (props) => {
  // store
  const [open, { setFalse, setTrue }] = useBoolean(false);
  const [loading, loadingAction] = useBoolean(false);
  const { studentId = '' } = useParams<any>();
  const { renderUserLabel } = useUserOptions();
  const { row, data } = props.dataSource;

  const genContractNoApi = useRequest(
    async () => await genContractNo({ studentId }),
    {
      manual: true,
    },
  );

  // action
  const handleOpen = async () => {
    try {
      await genContractNoApi.runAsync();
      setTrue();
    } catch (error: any) {
      console.log(error);
      message.error(error?.message || 'error');
    }
  };
  const handleDownload = async () => {
    loadingAction.setTrue();
    const doc = new jsPDF({
      orientation: 'p',
      unit: 'mm',
      format: [210, 297],
    });
    for (let i = 1; i < 5; i++) {
      let elem = document.getElementById('page1' + i) as HTMLDivElement;
      let canvas = await html2canvas(elem, { scale: 2 });
      let dataURI = canvas.toDataURL('image/jpeg');
      doc.addImage(dataURI, 'JPEG', 0.1, 0.1, 210, 297);
      if (i !== 4) doc.addPage();
    }
    doc.save(
      `大学院文科【${genContractNoApi?.data?.contractNo}】${data?.name}.pdf`,
    );
    setFalse();
    loadingAction.setFalse();
  };

  return (
    <>
      <Modal
        width={1142}
        open={open}
        title="生成合同"
        onCancel={setFalse}
        okText="下载"
        okButtonProps={{
          loading,
        }}
        destroyOnClose
        onOk={handleDownload}
      >
        <div className="flex flex-col justify-center tracking-wide font-mono render-joint-watermark">
          <div
            id="page11"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic"></span>
                <div className="render-joint-icon"></div>
              </div>
              <div>
                合同编码：
                <span className="underline">
                  {genContractNoApi?.data?.contractNo}
                </span>
              </div>
              <div>
                经办人：{renderData(renderUserLabel(row?.accept_teacher))}
                {/* &nbsp;&nbsp;&nbsp;&nbsp; 来源：{renderData(row?.custom_source)} */}
              </div>
              <div>
                销售老师：{renderData(renderUserLabel(data?.sale_teacher1))}
              </div>
              <div className="text-center mt-10 text-4xl font-bold">
                知新学苑班课课程服务协议
              </div>
              <div className="mt-28 text-xl">
                <div className="mt-6 text-xl">
                  <span className="font-bold">甲方（本机构）：</span>
                  知新学苑
                </div>
                <div className="mt-6 text-xl">联系电话：+81-0359565795</div>
                <div className="mt-6 text-xl">
                  地址：東京都豐島区池袋 1-7-18 Sunshine SCH ビル5F
                </div>
                {/* <div className="mt-6 text-xl pl-20">
                  東京都豊島区南池袋 2-30-12 BIT ビル 3F
                </div> */}
                <div className="mt-6 font-bold">
                  乙方（学员）: {renderData(data?.name)}
                </div>
                {/* <div className="mt-6">
                  身份证号：{renderData(data?.CN_ID_no)}
                </div> */}
                {/* <div className="mt-6">
                  在留卡号（如有）:{renderData(data?.JP_ID_no)}
                </div> */}
                // TODO: no date of birth
                <div className="mt-6">
                  手机号码：{renderData(data?.tel_jp || data?.tel_cn)}
                  &nbsp;&nbsp;&nbsp;&nbsp; 生日年月：
                  {renderData(data?.lang_school)}
                </div>
                <div className="mt-6">地址：{renderData(data?.address)}</div>
                <div className="mt-6">
                  乙方监护人/紧急联系人:{renderData(data?.emergency_name)}
                  &nbsp;&nbsp;&nbsp;&nbsp; 与联系人关系:
                  {renderData(data?.emergency_relationship)}
                </div>
                <div className="mt-6">
                  手机号码:{renderData(data?.emergency_tel)}
                </div>
                <div className="mt-8 leading-loose indent-8">
                  根据《中华人民共和国民法典》
                  <div className="w-0 inline-block" />
                  《中华人民共和国合同法》等有关法律法规的规定，甲、乙双方遵循平等、自愿、公平、诚实、守信的原则，经协商一致，签署本合同。
                </div>
              </div>
              <div className="mt-20">
                <div className="mt-6 text-lg font-bold">一、报名课程</div>
                <div className="mt-6 text-lg font-bold indent-8">
                  学员所报课程为：{renderData(row?.course_name)}
                </div>
                <div className="mt-6 text-lg font-bold indent-8">
                  课程有效期至：
                  {renderData(dayjs(row?.expired_date).format('YYYY年MM月DD'))}
                </div>
              </div>
              <div className="mt-20 text-lg font-bold">二、课程费用</div>
              <div className="mt-6 text-lg font-bold indent-8">
                课程总价:{renderData(formatterEn(row?.original_price))}
                &nbsp;&nbsp;&nbsp;&nbsp; 实缴金额:
                {renderData(formatterEn(row?.final_price))}
              </div>
              <div className="mt-6 text-lg font-bold indent-8">
                其他:{renderData(formatterEn(row?.off_price))}
              </div>
            </div>
          </div>




          <div
            id="page12"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic"></span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-8">
                <div className="mt-6 font-bold text-lg indent-0">
                  三、甲方（本机构）的权利义务
                </div>
                <div>
                  1.甲方有权按照国家有关政策规定和本合同约定收取课程及服务费用。
                </div>
                <div>
                  2.甲方应做好消防、食品、公共卫生等安全管理，落实安全防范措施，建立健全安全管理制度和应急预警处理机制，防范各类安全责任事故发生。
                </div>
                <div>
                  3.甲方应当依法保护在签订和履行合同过程中获悉的乙方及学员个人信息，不得泄露或者以其它不正当的方式使用乙方及学员个人隐私信息。
                </div>
                <div>
                  4.甲方开设课程符合国家及课程场所所在地有关规定，遵循教育规律和学生身心发展规律，甲方及甲方的教职员工不得以任何方式向学员传播、讲授违反相关法律法规的内容。
                </div>
                <div>
                  5.如发生特殊情况（例如自然灾害，任课教师不可避免的事病假等情况），甲方可在合理范围内对教学时间及任课教师做相应的调整以保证课程的正常进行；
                </div>
                <div>
                  6.如乙方学员不配合甲方的教学安排（包含但不限于课程出席、作业提交、定期测试、复习进度汇报等），甲方有权中止乙方的课程服务并通知其监护人。
                </div>
                <div>
                  7.乙方在此确认并同意，甲方可在不披露其姓名、联系方式、肖像等个人身份信息的前提下，将其录取结果、所报考专业、目标学校等信息用于宣传推广、市场展示及教学成果展示等非商业性用途。若甲方需使用乙方照片、姓名等个人信息，须另行征得乙方同意。
                </div>
                <div>
                  8.如服务进行过程中如有出现合同难以维继、合同目的难以实现、履行合同情况恶劣，以致甲方难以继续为乙方提供服务的情形时，甲方有权提出解除合同，退款参照第九项退款规则进行。
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  四、乙方（学员方）的权利义务
                </div>
                <div>
                  1.乙方有按照本合同的约定接受甲方课程服务的权利。服务期限内，甲方有义务为甲方规划考学方案，提供学校的出愿信息、考试信息，为乙方提供教务答疑服务。乙方应配合甲方的课程安排，遵守课程纪律，按时打卡签到，完成老师布置的作业；
                </div>
                <div>
                  2.乙方应当按时足额向甲方缴纳所报名课程费用。乙方应在合同签订之日起7日内缴纳全部学费。学费到账后，甲方可为学员办理入学手续，安排课程服务。
                </div>
                <div>
                  3.乙方及学员应当自觉遵守甲方的各项课程管理制度和课堂纪律，不得妨碍其他学员的正常学习活动。乙方应当自觉遵守培训场所的各种安全规定，不从事危害自身或者他人人身、财产安全的不当行为。培训期间如因乙方或学员的原因造成甲方或他人人身、财产损害的，乙方应承担损害赔偿责任。
                </div>
                <div>
                  4.乙方应通过官方学习平台或指定学习群体与甲方人员沟通。未经书面许可，乙方不得通过私人渠道（如微信、LINE等）与甲方讲师、教务建立私下联系，甲方不对由此产生的纠纷或法律责任承担责任。
                </div>
                <div className='font-bold'>
                  5.乙方及学员应当尊重甲方的知识产权，不得擅自对课程进行录音、录像。对于甲方拥有知识产权的纸质版、电子版材料或者课件，乙方及学员除在正常学习过程中合理使用外，不得私自复制、散发、销售，不得通过互联网进行分享、扩散和传播。
                </div>
                <div >
                  6.乙方不得擅自将本合同课程转让给第三方，或者将听课凭证转让、出借给他人使用。
                </div>
                <div></div>
                <div className="mt-6 font-bold text-lg indent-0">
                  五、变更课程
                </div>
                <div>
                  乙方可以申请变更课程。需要变更课程的学员，先联系教务老师提出变更课程申请，教务老师会联系专业老师和同学进行沟通，学员沟通后确定要变更课程的，可以为同学办理课程变更手续。变更前已开设课程费用需根据课程单价计算后另外扣除，课程差价的退/补参照合同解约规则进行。
                </div>
              </div>
            </div>
          </div>

          <div
            id="page13"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic"></span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-8">
                <div className="mt-6 font-bold text-lg indent-0">
                  六、违约责任
                </div>
                <div>
                  1.由于甲方的原因，包括但不限于被吊销营业执照（或事业单位法人证书、民办非企业单位登记证书），被责令停业整顿等原因，无法继续向乙方提供培训服务的，乙方有权要求解除合同，要求甲方退还剩余学费。
                </div>
                <div>
                  2.因甲方违约，双方就退费事宜书面达成一致后，甲方应于{' '}
                  {renderStrong('20')}{' '}
                  个工作日内将各项相关费用支付给乙方，每逾期一日应按逾期金额[{' '}
                  {renderStrong('0.065')} ]%的标准向乙方支付违约金。
                </div>
                <div>
                  3.乙方逾期未支付学费，经甲方 {renderStrong('3')}{' '}
                  次催缴后仍不支付学费的，甲方有权中止或终止课程服务。
                </div>
                <div>
                  4.乙方擅自将本合同课程转让给第三方，或者将听课凭证转让、出借给他人使用的，甲方有权拒绝为非学员提供培训服务。
                </div>
                <div>
                  5.由于乙方的原因，无法继续接受服务的，本合同解除，甲方不承担违约责任。如乙方提交虚假信息，致使本协议无法正常履行；乙方违反中日两国国家法律，被追究刑事责任，或受到遣送回国处分等情形。
                </div>
                <div>
                  6.因战争、自然灾害、传染性疾病等不可抗力致使本合同无法继续履行的，双方互不承担违约责任，受不可抗力影响的一方应及时书面通知对方，双方按照实际进行课时结算费用。
                </div>
                <div>
                  7.如乙方损坏甲方教学设施及用品，乙方应按照甲方实际产生的损失金额进行赔偿。
                </div>
                <div className="font-bold text-lg indent-0">七、退费标准：</div>
                <div>
                  因学员自身的原因提出解约并要求退款时，除已经产生的授课费、教务管理费不予退还外，学员还应承担相当于学费原价{' '}{renderStrong('30%')} 的违约金。
                  授课费、教务管理费自报名之日起，均摊到月（不足整月的，按整月计算）。
                </div>
                <div className='indent-0'>
                  具体计算方法如下：
                </div>
                <div>
                  退课费 = 实交总学费－已经产生的授课费和教务管理费－违约金。
                </div>
                <div>
                  ※ 不限定服务年限的套餐产生退课，服务期限按 24 个月计算。
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  十一、争议解决
                </div>
                <div>
                  本合同在履行过程中发生争议，双方可协商解决，协商不成的，双方可依法向仲裁委员会申请仲裁或向人民法院提起诉讼。
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  十二、补充条款
                </div>
                <div>
                  本合同未尽事宜，由下列补充条款进行约定。
                  {renderStrong(
                    '补充条款与本合同其他条款不一致的，以补充条款为准。',
                  )}
                </div>
                <div className="whitespace-pre-wrap break-words indent-0 pl-8">
                  {data?.contract_note}
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  十三、合同生效
                </div>
                <div>本合同自甲方盖章乙方签署之日起生效。</div>
                <div>
                  合同正本连同补充条款共四页，{renderStrong('一式两份')}
                  ，甲乙双方各执一份，各份具有{renderStrong('同等')}法律效力。
                </div>

                <div className="font-bold text-lg indent-0">十四、合同附件</div>
                <div className='text-center font-bold text-base'>保密协议</div>
                <div className='indent-0'>第一条 保密信息的定义与适用范围</div>
                <div>乙方在接受甲方服务过程中，可能接触或知悉涉及甲方商业运营的相关资料与信息。乙方明确确认，上述信息属于甲方依法享有权利的知识产权范畴，构成甲方专属的无形资产。无论是否标明“机密”“保密”或“秘密”等字样，均应视为保密信息。该等信息包括但不限于：</div>
                <div>1.经营信息：如运营策略、财务数据、人力资源管理、市场推广资料等；</div>
                <div>2.乙方相关信息：如报名资料、考试结果、录取情况等；</div>

              </div>
            </div>
          </div>

          <div
            id="page14"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic"></span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-8">
                <div>3.根据法律、行政法规或合同义务应予保密的内容；</div>
                <div>4.经甲方书面确认需保密的其他事项。</div>
                <div className='indent-0'>第二条 乙方的保密义务</div>
                <div>乙方同意遵守以下保密义务：</div>
                <div>1.未经甲方书面许可，不得以任何形式向第三方泄露、传播、公开或披露甲方的保密信息；</div>
                <div>2.不得擅自使用甲方资料为第三方提供咨询、服务或产品等；</div>
                <div>3.乙方应视保密信息为甲方的重要资产，任何疏忽或泄露行为将被视为违约行为；</div>
                <div>4.与甲方运营或教学相关的设备、工具、软件、文档、音视频资料、数据库等均为甲方资产，未经授权乙方不得复制、下载、传播或带离；</div>
                <div>5.不得协助、教唆或默许第三方获取、使用甲方的保密信息；</div>
                <div>6.于本协议有效期内及终止后，乙方仍负有不泄露、不擅用保密信息的持续义务，不得将该等信息提供给任何第三方使用；</div>
                <div>7.即便乙方因任何原因中止合作、退出项目或解除协议，仍应承担与在职期间相同的保密义务，严禁将甲方持有的信息为他方服务。</div>

                <div className='indent-0'>第三条 甲方的保密义务</div>
                <div>甲方承诺对乙方信息承担如下保密义务：</div>
                <div>1.未经乙方书面同意，甲方不得向任何第三方泄露乙方个人身份证明类信息（如身份证号、出生日期、住址等）；</div>
                <div>2.未经乙方授权，甲方不得向第三方提供乙方联系方式（如电话号码、微信号、电子邮箱等）；</div>
                <div>3.对乙方特别提出要求保密的其他信息，甲方亦应承担相应保密责任。</div>

                <div className='indent-0'>第四条 保密期限</div>
                <div>乙方承担保密义务的期限自离开机构之日起三年起生效，持续至合作终止之日起三年。</div>
                <div>甲方对乙方的保密义务则长期有效，不因合作终止而失效。</div>

                <div className='indent-0'>第五条 违约责任</div>
                <div>1.若乙方在保密义务有效期内违反本协议，因乙方违约造成甲方实际损失，乙方还应就该等损失全额赔偿，包括但不限于直接损失、间接损失、甲方为调查违约行为所支出的合理费用（如律师费、诉讼费等）。违约金不视为赔偿金替代，甲方可同时主张两者。</div>
                <div>2.若乙方违反本协议构成违法犯罪行为，甲方有权依法向相关机关举报，并协助立案侦查。乙方应就由此引起的全部损害结果承担法律责任。</div>
                <div>3.如甲方违反保密义务，乙方可依法提出补偿主张；若协商无果，乙方可通过司法渠道寻求救济。</div>

              </div>
              <div className="mt-20 flex">
                <div className="w-1/2">
                  <div>{renderStrong('甲方（盖章）：')} 知心文苑</div>
                  <div className="mt-10 font-bold"></div>
                  <div className="font-bold"></div>
                  <div className="mt-10 font-bold">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日
                  </div>
                </div>
                <div className="w-1/2">
                  <div className="font-bold">乙方（签字）：</div>
                  <div className="mt-10 font-bold"></div>
                  <div className="font-bold"></div>
                  <div className="mt-10 font-bold">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>
      <Button
        size="small"
        loading={genContractNoApi.loading}
        onClick={handleOpen}
      >
        大学院文科
      </Button>
    </>
  );
};

export default RenderJoint;
