import { Descriptions } from 'antd';
////
import { renderDate } from '@/hooks/useDayjs';
import { Access, useAccess } from '@/hooks/useAccess';

export interface StudentInfoProps {
  dataSource: API.Student;
  canReadAll: Boolean;
}

const StudentInfo: React.FC<StudentInfoProps> = (props) => {
  const access = useAccess();
    return (
      <Access accessible={access.page?.['studentInfo.U']}>
        <Descriptions size="small" bordered column={2}>
          <Descriptions.Item label="在留卡号">
            {props.dataSource?.JP_ID_no}
          </Descriptions.Item>
          <Descriptions.Item label="身份证号">
            {props.dataSource?.CN_ID_no}
          </Descriptions.Item>
          <Descriptions.Item label="手机号码（日本）">
            {props.dataSource?.tel_jp}
          </Descriptions.Item>
          <Descriptions.Item label="手机号码（中国）">
            {props.dataSource?.tel_cn}
          </Descriptions.Item>
          <Descriptions.Item label="住址" span={2}>
            {props.dataSource?.address}
          </Descriptions.Item>
          <Descriptions.Item label="紧急联系人姓名">
            {props.dataSource?.emergency_name}
          </Descriptions.Item>
          <Descriptions.Item label="与紧急联系人的关系">
            {props.dataSource?.emergency_relationship}
          </Descriptions.Item>
          <Descriptions.Item label="紧急联系人手机号">
            {props.dataSource?.emergency_tel}
          </Descriptions.Item>
          <Descriptions.Item label="紧急联系人微信号">
            {props.dataSource?.emergency_wechat}
          </Descriptions.Item>
        </Descriptions>
      </Access>
    );
};

export default StudentInfo;
