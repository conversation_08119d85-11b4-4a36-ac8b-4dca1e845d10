import { useParams } from 'umi';
import { useRequest } from 'ahooks';
import { Button, message, Modal, Table } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
////
import { useForm } from '@/hooks/useSKForm';
import {
  getStudentById,
  addPaymentHistoryToStudent,
  updatePaymentHistoryToStudent,
  removePaymentHistoryFromStudent,
  addGoalToStudent,
  updateGoalFromStudent,
  removeGoalFromStudent,
} from '@/services/request/student';
import { renderDate } from '@/hooks/useDayjs';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import { useAccess, Access, renderAccess } from '@/hooks/useAccess';
import GoalInfoForm from '../../components/GoalInfoForm';
import { examDateCategory, goalLevelCategory, goalStatusCategory } from '@/utils/formConsts';

export interface GoalInfoProps {}

const GoalInfo: React.FC<GoalInfoProps> = () => {
  // state
  const access = useAccess();
  const { formType, formProps, handleOpen } = useForm<API.GoalInfo>();
  const { studentId = '' } = useParams<any>();

  // api
  const studentAPI = useRequest(
    async () => await getStudentById({ studentId }),
    { cacheKey: 'studentById' },
  );

  // action
  const handleAdd = () => {
    handleOpen({
      title: '新建升学信息',
      type: 'add',
      data: null,
    });
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await addGoalToStudent({ studentId, ...v });
      }
      if (formType === 'edit') {
        if (!formProps.dataSource?._id) throw '';
        await updateGoalFromStudent({
          studentId,
          goalId: formProps.dataSource?._id,
          goal: v,
        });
      }
      studentAPI.refresh();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  // render
  const RenderStudentGoalInfoActions = renderAccess({
    accessible: access.page?.['goalInfo.D'],
    children: (
      <Table.ColumnGroup title="操作">
        {renderAccess({
          accessible: access.page?.['goalInfo.D'],
          children: (
            <Table.Column
              title="编辑"
              fixed="right"
              width={50}
              render={(row) => {
                const handleEdit = () => {
                  handleOpen({
                    title: '编辑升学信息',
                    type: 'edit',
                    data: row,
                  });
                };
                return (
                  <Button size="small" onClick={handleEdit}>
                    <EditOutlined />
                  </Button>
                );
              }}
            />
          ),
        })}
        {renderAccess({
          accessible: access.page?.['goalInfo.D'],
          children: (
            <Table.Column
              title="删除"
              fixed="right"
              width={50}
              render={(row) => {
                const handleDelete = () => {
                  Modal.confirm({
                    centered: true,
                    title: '删除',
                    content: '确认删除本条升学信息，删除后无法恢复。',
                    onOk: async () => {
                      await removeGoalFromStudent({
                        studentId,
                        GoalId: row?._id,
                      });
                      await studentAPI.refreshAsync();
                    },
                    okText: '确认',
                    cancelText: '取消',
                  });
                };
                return (
                  <Button size="small" onClick={handleDelete}>
                    <DeleteOutlined />
                  </Button>
                );
              }}
            />
          ),
        })}
      </Table.ColumnGroup>
    ),
  });

  return (
    <>
      <GoalInfoForm type={formType} {...formProps} onSubmit={handleSubmit} />
      <Access accessible={access.page?.['goalInfo.D']}>
        <Button className="mb-4" type="primary" onClick={handleAdd} disabled={studentAPI?.data?.goal_lists.length > 0}>
          新建
        </Button>
      </Access>
      <Table
        rowKey="_id"
        dataSource={studentAPI?.data?.goal_lists}
        size="small"
      >
        <Table.Column
          title="评级"
          dataIndex="level"
          render={renderLabel(goalLevelCategory)}
        />
        <Table.Column
          title="计划考试时间"
          dataIndex="exam_date"
          render={renderLabel(examDateCategory)}
        />
        <Table.Column
          title="目前状态"
          dataIndex="current_status"
          render={renderLabel(goalStatusCategory)}
        />
        <Table.Column
          title="套磁状态"
          dataIndex="is_taoci"
          render={renderLabel(studentJSON.Boolean)}
        />
        <Table.Column title="套磁院校和教授" dataIndex="taoci_detail" />
        <Table.Column title="套磁结果" dataIndex="taoci_result" />
        <Table.Column title="报考院校" dataIndex="baokao_univ" />
        <Table.Column title="合格院校" dataIndex="hege_univ" />
        <Table.Column title="合格经验" dataIndex="hege_jingyan" />
        <Table.Column title="近况" dataIndex="jinkuang" />
        {RenderStudentGoalInfoActions}
      </Table>
    </>
  );
};

export default GoalInfo;
