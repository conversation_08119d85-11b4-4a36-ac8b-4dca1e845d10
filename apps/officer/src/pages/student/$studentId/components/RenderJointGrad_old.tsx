import { useBoolean, useRequest } from 'ahooks';
import { Button, Modal, message } from 'antd';
import dayjs from 'dayjs';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
////
import { useUserOptions } from '@/hooks/useApiOptions';
import { genContractNo } from '@/services/request/student';
import { useParams } from 'umi';
import { formatterEn } from '@/utils/function';

export interface RenderJointProps {
  dataSource: {
    row: API.Course;
    data: API.Student;
  };
}

const renderData = (data: any) => (
  <span className="underline">{`\u2002\u2002\u2002\u2002${
    data || ''
  }\u2002\u2002\u2002\u2002`}</span>
);

const renderStrong = (data: any) => <span className="font-bold">{data}</span>;

const RenderJoint: React.FC<RenderJointProps> = (props) => {
  // store
  const [open, { setFalse, setTrue }] = useBoolean(false);
  const [loading, loadingAction] = useBoolean(false);
  const { studentId = '' } = useParams<any>();
  const { renderUserLabel } = useUserOptions();
  const { row, data } = props.dataSource;

  const genContractNoApi = useRequest(
    async () => await genContractNo({ studentId }),
    {
      manual: true,
    },
  );

  // action
  const handleOpen = async () => {
    try {
      await genContractNoApi.runAsync();
      setTrue();
    } catch (error: any) {
      console.log(error);
      message.error(error?.message || 'error');
    }
  };
  const handleDownload = async () => {
    loadingAction.setTrue();
    const doc = new jsPDF({
      orientation: 'p',
      unit: 'mm',
      format: [210, 297],
    });
    for (let i = 1; i < 7; i++) {
      let elem = document.getElementById('page0' + i) as HTMLDivElement;
      let canvas = await html2canvas(elem, { scale: 2 });
      let dataURI = canvas.toDataURL('image/jpeg');
      doc.addImage(dataURI, 'JPEG', 0.1, 0.1, 210, 297);
      if (i !== 6) doc.addPage();
    }
    doc.save(`合同【${genContractNoApi?.data?.contractNo}】${data?.name}.pdf`);
    setFalse();
    loadingAction.setFalse();
  };

  return (
    <>
      <Modal
        width={1142}
        open={open}
        title="生成合同"
        onCancel={setFalse}
        okText="下载"
        okButtonProps={{
          loading,
        }}
        destroyOnClose
        onOk={handleDownload}
      >
        <div className="flex flex-col justify-center tracking-wide font-mono render-joint-watermark">
          <div
            id="page01"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·大学院</span>
                <div className="render-joint-icon"></div>
              </div>
              <div>
                合同编码：
                <span className="underline">
                  {genContractNoApi?.data?.contractNo}
                </span>
              </div>
              <div>
                经办人：{renderData(renderUserLabel(row?.accept_teacher))}
                {/* &nbsp;&nbsp;&nbsp;&nbsp; 来源：{renderData(row?.custom_source)} */}
              </div>
              <div>
                销售老师：{renderData(renderUserLabel(data?.sale_teacher1))}
              </div>
              <div className="text-center mt-10 text-4xl font-bold">
                新领域理工塾课程服务协议
              </div>
              <div className="mt-28 text-xl">
                <div className="mt-6 text-xl">
                  <span className="font-bold">甲方（本机构）：</span>
                  新领域理工塾(SHINRYOIKIRIKOJUKU)
                </div>
                <div className="mt-6 text-xl">联系电话：+81-0359565795</div>
                <div className="mt-6 text-xl">
                  地址：東京都豐島区池袋 1-7-18 Sunshine SCH ビル5F
                </div>
                <div className="mt-6 font-bold">
                  乙方（学员）: {renderData(data?.name)}
                </div>
                <div className="mt-6">
                  身份证号：{renderData(data?.CN_ID_no)}
                </div>
                <div className="mt-6">
                  在留卡号（如有）:{renderData(data?.JP_ID_no)}
                </div>
                <div className="mt-6">
                  手机号码：{renderData(data?.tel_jp || data?.tel_cn)}
                  &nbsp;&nbsp;&nbsp;&nbsp; 语言学校：
                  {renderData(data?.lang_school)}
                </div>
                <div className="mt-6">地址：{renderData(data?.address)}</div>
                <div className="mt-6">
                  乙方监护人/紧急联系人:{renderData(data?.emergency_name)}
                  &nbsp;&nbsp;&nbsp;&nbsp; 与联系人关系:
                  {renderData(data?.emergency_relationship)}
                </div>
                <div className="mt-6">
                  手机号码:{renderData(data?.emergency_tel)}
                  &nbsp;&nbsp;&nbsp;&nbsp; 微信号:
                  {renderData(data?.emergency_wechat)}
                </div>
                <div className="mt-8 leading-loose indent-8">
                  根据《中华人民共和国民法典》《中华人民共和国合同法》等有关法律法规的规定，甲、乙双方遵循平等、自愿、公平、诚实、守信的原则，经协商一致，签署本合同。
                </div>
              </div>
              <div className="mt-20">
                <div className="mt-6 text-lg font-bold">一、报名课程</div>
                <div className="mt-6 text-lg font-bold indent-8">
                  学员所报课程为：{renderData(row?.course_name)}
                </div>
                <div className="mt-6 text-lg font-bold indent-8">
                  课程有效期至：
                  {renderData(dayjs(row?.expired_date).format('YYYY年MM月DD'))}
                </div>
              </div>
            </div>
          </div>

          <div
            id="page02"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·大学院</span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 text-lg font-bold">二、课程费用</div>
              <div className="mt-6 text-lg font-bold indent-8">
                课程总价:{renderData(formatterEn(row?.original_price))}
                &nbsp;&nbsp;&nbsp;&nbsp; 实缴金额:
                {renderData(formatterEn(row?.final_price))}
              </div>
              <div className="mt-6 text-lg font-bold indent-8">
                其他:{renderData(formatterEn(row?.off_price))}
              </div>

              <div className="mt-20 leading-8 indent-8">
                <div className="font-bold text-lg indent-0">
                  三、课程服务范围
                </div>
                <div className="indent-0">（一）上课形式：</div>
                <div>
                  新领域理工塾提供线上直播课程，线下课程以及录播课程三种形式(学生可根据自身情况自由选择上课方式）。线上课程专为在国内的学生，距离私塾较远的塾内学生开设。每节课均会进行自动录制并上传到云端，若学生没及时参加直播课程，在提前告知私塾缺席理由的前提下，在合同有效期内我们提供给学生无次数限制，无时长限制的录播课程。线下课程在教室进行，讲师会使用塾内电子设备书写，并投影到教室的屏幕，线下学生可以直接参与课堂互动。建议线上学生全程开麦，积极参与和配合讲师。线下学生上课前需要提前来讲台前领取上课的讲义和其他资料，并提交上次课程的作业。建议所有报名的学生，积极参加线下课程，和老师积极互动完成每次课程的作业，每期课程的测试，以保证更好的学习效果。
                </div>
                <div className="indent-0">（二）课程内容：</div>
                <div>1.专业课：</div>
                <div>
                  无课时限制，错过直播课程可以反复观看录播。学生可以自由选课，根据自己需要的课程进行选择，私塾的班主任（或者规划组）也会给出选课的建议。
                </div>
                <div>
                  如有需要其他学院课程可以直接提出需求，例如：情报的考生需要电子电气的相关课程，机器人方向可以兼修情报和机械课程。
                </div>
                <div>每节专业课需要同学做到出席打卡签到，提交作业。</div>
                <div>
                  线上同学需要在 zoom
                  房间内修改昵称为真实姓名，提交作业，积极与老师互动。2.专业习题课：
                </div>
                <div>
                  专业课开课两周之后会开设专业习题课，主要讲解专业课留下来的作业。专业习题课均为各所大学的过去问原题。
                </div>
                <div>
                  习题课同专业课一样，需要的同学根据自己的情况进行选择。线下专业课需要同学做到出席打卡签到，提交作业；
                </div>
                <div>
                  线上同学需要在 zoom
                  房间内修改昵称为真实姓名，提交作业，积极与老师互动。3.过去问课：
                </div>
                <div>
                  过去问课程每年会固定开设热门大学过去问讲解，（并非涵盖日本所有的大学院院校过去问）。在完成习题册之后，会集中以大学的类别进行讲解。
                </div>
                <div>
                  如有未能覆盖到的大学和院校，同学有相关需要可以整理自己的问题告知私塾。私塾方面会帮助学生提供集中的答疑。如需要解答答案，私塾方面则会尽最大可能联系专业课老师协同处理大家的问题。
                </div>
                <div>4.语言课：</div>
                <div>
                  语言课分为日语等级课程及英语课程两种，同学报名时可根据自身需求进行选择。其中日语等级课程包含
                  N4 等级班，N3 等级班，N2 等级班，N1
                  冲刺班四种不同的班级，英语课
                </div>
              </div>
            </div>
          </div>

          <div
            id="page03"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·大学院</span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-8">
                <div>程中包含托福对策班，托业对策班两种班级。</div>
                <div>
                  日语等级课程中 N1 冲刺班每年循环开课两期，N4~N2
                  等级班每年分别循环开课四期，英语课程中托福托业对策班每年分别循环开课三期，同学可根据自己的需求和基础进行选择。
                </div>
                <div>5.升学对策课：</div>
                <div>
                  课程辅导内容涵盖内容：专业讲解（包含传统大类与融合专业）、考学时间规划、教授与研究室信息检索与选择、套辞信书写（日文与英文）、见学与入试说明会、报名前内诺面试、研究计划书、文献阅读、考试面试等。
                </div>
                <div className="font-bold text-lg indent-0">
                  四、甲方（本机构）的权利义务
                </div>
                <div>
                  1.甲方有权按照国家有关政策规定和本合同约定收取课程及服务费用。
                </div>
                <div>
                  2.甲方应做好消防、食品、公共卫生等安全管理，落实安全防范措施，建立健全安全管理制度和应急预警处理机制，防范各类安全责任事故发生。
                </div>
                <div>
                  3.甲方应当依法保护在签订和履行合同过程中获悉的乙方及学员个人信息，不得泄露或者以其它不正当的方式使用乙方及学员个人隐私信息。未经乙方同意，甲方不得擅自使用乙方及学员的姓名、肖像、影像用于商业用途。
                </div>
                <div>
                  4.甲方开设课程符合国家及课程场所所在地有关规定，遵循教育规律和学生身心发展规律，甲方及甲方的教职员工不得以任何方式向学员传播、讲授违反相关法律法规的内容。
                </div>
                <div>
                  5.如发生特殊情况（例如自然灾害，任课教师不可避免的事病假等情况），甲方可在合理范围内对教学时间及任课教师做相应的调整以保证课程的正常进行；
                </div>
                <div>
                  6.如乙方学员不配合甲方的教学安排（包含但不限于课程出席、作业提交、定期测试、复习进度汇报等），经再三交涉仍不配合的情形下，甲方有权中止乙方的课程服务并通知其监护人。
                </div>
                <div>
                  7.乙方学员经过学习顺利录取院校后，甲方有知悉学员合格院校及相关信息的权利，甲方有使用乙方学员非个人信息以外的录取信息进行宣传的权利。
                </div>
                <div>
                  8.如服务进行过程中如有出现合同难以维继、合同目的难以实现、履行合同情况恶劣，以致甲方难以继续为乙方提供服务的情形时，甲方有权提出解除合同，退款参照第九项退款规则进行。
                </div>
                <div className="font-bold text-lg indent-0">
                  五、乙方（学员方）的权利义务
                </div>
                <div>
                  1.乙方有按照本合同的约定接受甲方课程服务的权利。乙方应配合甲方的课程安排，遵守课程纪律，按时打卡签到，完成老师布置的作业；
                </div>
                <div>
                  2.乙方有权享受甲方提供的升学文书类服务；包括志望理由书，联系教授及研究室邮件，报名材料，阅读文献等文书类的讲解和有关文法、语言规范的修改服务；（详见附件二.《新领域理工塾文书类服务范围》）
                </div>
                <div>
                  3.乙方有权享受甲方提供的模拟面试服务；乙方可以报名 6
                  次日语/英语模拟面试，学员需要提前一周预约，准备面试稿提交给面试老师，进行模拟面试练习，以帮助同学更好的适应考学面试环节；
                </div>
                <div>
                  4.乙方有权享受甲方提供的模拟考试服务；甲方会根据课程进度为同学安排定期测试，结课考试，辅助同学验收课程学习成果。
                </div>
                <div>
                  5.已报名课程并已分配班主任的乙方学员，有向班主任寻求课业上答疑解惑的权利。
                </div>
                <div>
                  6.乙方应当按时足额向甲方缴纳所报名课程费用。乙方应在合同签订之日起7
                  日内缴纳全部学费。学费到账后，甲方可为学员办理入学手续，安排课程服务。
                </div>
                <div>
                  7.乙方及学员应当自觉遵守甲方的各项课程管理制度和课堂纪律，不得妨碍其他学员的正常学习活动。乙方应当自觉遵守培训场所的各种安全规定，不从事危害自身或者他人人身、
                </div>
              </div>
            </div>
          </div>

          <div
            id="page04"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·大学院</span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-8">
                <div>
                  财产安全的不当行为。培训期间如因乙方或学员的原因造成甲方或他人人身、财产损害的，乙方应承担损害赔偿责任。
                </div>
                <div>
                  8.乙方及学员应当尊重甲方的知识产权，不得擅自对课程进行录音、录像。对于甲方拥有知识产权的纸质版、电子版材料或者课件，乙方及学员除在正常学习过程中合理使用外，不得私自复制、散发、销售，不得通过互联网进行分享、扩散和传播。
                </div>
                <div>
                  9.乙方不得擅自将本合同课程转让给第三方，或者将听课凭证转让、出借给他人使用。
                </div>
                <div className="font-bold text-lg indent-0">六、变更课程</div>
                <div>
                  乙方学员可以申请变更课程。需要变更课程的学员，先联系教务老师提出变更课程申请，教务老师会联系专业老师和同学进行沟通，学员沟通后确定要变更课程的，可以为同学办理课程变更手续。变更前已开设课程费用需根据课程单价计算后另外扣除，课程差价的退/补参照合同解约规则进行。
                </div>
                <div className="font-bold text-lg indent-0">七、合同续约</div>
                <div>
                  乙方报名甲方套餐课程服务后，如在合同到期之日前 30
                  日，向甲方提出续约申请，甲方有权根据乙方在合约期间内的上课情况、学习态度等情况决定是否为乙方续约。
                </div>
                <div>具体审核标准及续费金额计算方法如下：</div>
                <div>
                  1.出席率及作业提交情况均在
                  80%以上时，续约费用为当期合约中套餐价格10%；
                </div>
                <div>
                  2.出席率及作业提交情况在
                  60%-80%时，续约费用为当期合约中套餐价格的20%；
                </div>
                <div>
                  3.出席率及作业提交情况低于 60%时，甲方有权拒绝为乙方办理续费。
                </div>
                <div>
                  （甲方会根据课程系统后台数据为乙方学员记录考勤，包含出勤情况、出勤时长等。学生的作业提交情况、任课老师的反馈将作为参考标准。同学登录课程平台上课时需使用真实姓名
                  ID，以免影响考勤记录情况。）
                </div>
                <div className="font-bold text-lg indent-0">八、违约责任</div>
                <div>
                  1.甲方未按照合同规定提供课程服务，或甲方未经乙方同意，擅自变更服务方式的，乙方有权要求解除合同，要求甲方退还剩余学费。
                </div>
                <div>
                  2.由于甲方的原因，包括但不限于被吊销营业执照（或事业单位法人证书、民办非企业单位登记证书），被责令停业整顿等原因，无法继续向乙方提供培训服务的，乙方有权要求解除合同，要求甲方退还剩余学费。
                </div>
                <div>
                  3.未经乙方同意，甲方擅自将本合同约定的服务转给第三方或将学员转交给第三方机构提供服务的，乙方有权要求解除合同，要求甲方退还剩余学费。
                </div>
                <div>
                  4.因甲方违约，双方就退费事宜书面达成一致后，甲方应于 20
                  个工作日内将各项相关费用支付给乙方，每逾期一日应按逾期金额[
                  0.065 ]%的标准向乙方支付违约金。
                </div>
                <div>
                  5.乙方逾期未支付学费，经甲方 3
                  次催缴后仍不支付学费的，甲方有权中止或终止课程服务，乙方需支付实际已学习课程的课时费、已预约教师的授课费、教务人员费、教材复印费，每逾期一日应按逾期金额[
                  0.065 ]%的标准向甲方支付违约金。
                </div>
                <div>
                  6.乙方擅自将本合同课程转让给第三方，或者将听课凭证转让、出借给他人使用的，甲方有权拒绝为非学员提供培训服务。
                </div>
                <div>
                  7.由于乙方的原因，无法继续接受服务的，本合同解除，甲方不承担违约责任。如乙方提交虚假信息，致使本协议无法正常履行；乙方违反中日两国国家法律，被追究刑事责任，或受到遣送回国处分等情形。
                </div>
                <div>
                  8.因战争、自然灾害、传染性疾病等不可抗力致使本合同无法继续履行的，双方互不承
                </div>
              </div>
            </div>
          </div>

          <div
            id="page05"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·大学院</span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-8">
                <div>
                  担违约责任，受不可抗力影响的一方应及时书面通知对方，双方按照实际消耗课时结算（或协商结算）费用。
                </div>
                <div>
                  9.如乙方损坏甲方教学设施及用品，乙方应按照甲方实际产生的损失金额进行赔偿。
                </div>
                <div className="font-bold text-lg indent-0">九、退费标准：</div>
                <div className="indent-0">1.套餐类课程</div>
                <div>
                  乙方在入学(以入学协议书签约日期为准)后 30
                  日内提出终止课程而要求退款时，甲方在扣除「课程老师咨询费用（2
                  万日元或等价人民币）」，「学生档案建立费用（5000
                  日元或等价人民币）」及「已开设课程费用（正在进行的课程按照甲方已开设回数的课程单价计算）」后，决定退款途径并向乙方退还剩余学费；
                </div>
                <div className="indent-0">2.单科类课程</div>
                <div>
                  乙方在单科课程开始 30
                  日内可提出退费申请，甲方扣除「课程老师咨询费用
                  {renderStrong('（2 万日元或等价人民币）')}
                  」，「学生档案建立费用
                  {renderStrong('（5000 日元或等价人民币）')}」及「
                  {renderStrong(
                    '已开设课程费用（正在进行的课程按照甲方已开设回数的课程单价计算）',
                  )}
                  」后，决定退款途径并向乙方退还剩余学费；
                </div>
                <div className="indent-0">
                  3.课程服务开始 30
                  日后,甲方概不受理乙方提出的任何理由及形式的退款申请；
                </div>
                <div className="indent-0">
                  4.第八条第（一）、（二）、（三）款中的情形
                </div>
                <div>
                  由于上述甲方行为造成课程服务无法进行时，在扣除「课程老师咨询费用
                  {renderStrong('（2 万日元或等价人民币）')}
                  」，「学生档案建立费用(
                  {renderStrong('（5000 日元或等价人民币）')}」，及「
                  {renderStrong(
                    '已开设课程费用（正在进行的课程按照已上回数的课程单价计算）',
                  )}
                  」后，甲方向乙方退还多余学费，并解除本协议。
                </div>
                <div className="indent-0">5.其他规定</div>
                <div>
                  (1).在前两项规定的情形下,乙方支付的学费等不足于「
                  {renderStrong('课程老师咨询费用')}」，「
                  {renderStrong('学生档案建立费用')}」及「
                  {renderStrong('已开设课程费用')}
                  」的总和时，乙方需向甲方支付差额部分；
                </div>
                <div>
                  (2).乙方要求退学或退款时，需要向甲方出示并归还
                  {renderStrong('领收书')}及{renderStrong('本入学协议书')}
                  ，且应由本人或法定代理人持委托书到甲方指定办公地点，出示相关身份证明，填写
                  {renderStrong('《终止服务协议》')}，完成退学手续后予以退款。
                </div>
                <div className="font-bold text-lg indent-0">
                  十、VIP 学员（一对一课程）
                </div>
                <div>
                  凡参加一对一授课的学员，需遵守 VIP
                  课程约课相关制度以及以下规定：
                </div>
                <div>
                  1.VIP/一对一课程预约申请须{renderStrong('提前 7 日')}
                  联系教务工作人员进行排课；因7、8 月为预约课程高峰期，
                  {renderStrong('7 月 1 日至 8 月 31 日')}期间，学员须提前
                  {renderStrong('至少 14 日')}通知教务工作人员进行排课。
                </div>
                <div className="font-bold">
                  2.所有授课必须在本塾指定校区或线上指定会议室进行，如出现学员私下预约老师至校外授课的情况，则甲方有权终止该学员的
                  VIP 课程服务并不予退款；
                </div>
                <div className="font-bold">
                  3.如学员在预约时间无法按时进行课程，需要提前至少 24
                  小时通知教务取消课程；如距离上课时间 24
                  小时以内提交申请，或课程当日不出席课程，则乙方有权扣除当日的课程时长，当日所产生的学费不予退还；
                </div>
                <div>
                  4.如任课教师在预约时间无法按时进行课程，必须提前
                  {renderStrong('至少 24 小时')}
                  通知甲方更改或取消课程；如任课教师距离上课时间
                  {renderStrong(' 24 小时以内')}
                  取消课程，乙方将按照预约课时的双倍时长补偿乙方；
                </div>
                <div>
                  5.因不可抗力（地震、水灾等自然因素；征收、征用等政府行为；战争，罢工等社会异
                </div>
              </div>
            </div>
          </div>

          <div
            id="page06"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·大学院</span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-8">
                <div>
                  常事件）原因所导致的学员不能在预约时间到达指定校区或线上指定会议室上课的情况，不扣除学习时长；
                </div>
                <div>
                  6.若任课教师迟到（非因不可抗力因素），则任课教师迟到的时长甲方将双倍补偿给乙方；
                </div>
                <div>
                  7.VIP
                  学员一对一课程，仅针对乙方的升学进行辅导，不适用于升学以外的其他任何指导需求。合约到期之后，VIP
                  服务即刻中止，剩余 VIP 时长自动作废。
                </div>
                <div>
                  8.乙方在 VIP 课程开始 {renderStrong('30 日')}
                  内可提出退费申请，甲方扣除「课程老师咨询费用
                  {renderStrong('（2万日元或等价人民币）')}」,「学生档案建立费用
                  {renderStrong('（5000日元或等价人民币）')}」及「
                  {renderStrong('已使用 VIP 课时费')}
                  」后，决定退款途径并向乙方退还剩余学费；
                </div>
                <div>
                  课程服务开始{renderStrong(' 30 日')}
                  后,甲方概不受理乙方提出的任何理由及形式的退款申请；
                </div>
                <div>
                  9.VIP
                  学员如有其他事项，由甲乙双方协商一致后增加补充条款，补充条款经双方签章后，效力与其他合同条款相同。
                </div>
                <div>十一、争议解决</div>
                <div>
                  本合同在履行过程中发生争议，双方可协商解决，协商不成的，双方可依法向仲裁委员会申请仲裁或向人民法院提起诉讼。
                </div>
                <div>十二、补充条款</div>
                <div>
                  本合同未尽事宜，由下列补充条款进行约定。
                  {renderStrong(
                    '补充条款与本合同其他条款不一致的，以补充条款为准。',
                  )}
                </div>
                <div className="whitespace-pre-wrap break-words indent-0 pl-8">
                  {data?.contract_note}
                </div>
                <div className="font-bold text-lg indent-0">十三、合同生效</div>
                <div>本合同自甲方盖章乙方签署之日起生效。</div>
                <div>
                  合同正本连同补充条款共六页，{renderStrong('一式两份')}
                  ，甲乙双方各执一份，各份具有{renderStrong('同等')}法律效力。
                </div>
                <div className="font-bold text-lg indent-0">十四、合同附件</div>
                <div>其他课程服务相关事项详见以下合同附件：</div>
                <div>1.附件一.《新领域理工塾文书类服务范围》</div>
                <div>2.附件二.《新领域理工塾课程服务范围》</div>
                <div>3.附件三.《新领域理工塾过去问：夏季部分》</div>
                <div>4.附件四.《新领域理工塾过去问：冬季部分》</div>
              </div>
              <div className="mt-20 flex">
                <div className="w-1/2">
                  <div className="font-bold">甲方（盖章）：</div>
                  <div className="mt-10 font-bold">新领域理工塾</div>
                  <div className="font-bold">SHINRYOIKIRIKOJUKU</div>
                  <div className="mt-10 font-bold">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日
                  </div>
                </div>
                <div className="w-1/2">
                  <div className="font-bold">乙方（签字）：</div>
                  <div className="mt-16 font-bold">乙方（监护人签字）：</div>
                  <div className="mt-10 font-bold">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>
      <Button
        size="small"
        loading={genContractNoApi.loading}
        onClick={handleOpen}
      >
        大学院
      </Button>
    </>
  );
};

export default RenderJoint;
