import { useParams } from 'umi';
import { useAntdTable } from 'ahooks';
import { Table } from 'antd';
////
import { fixParams } from '@/utils/function';
import { getAllVipCourses } from '@/services/request/vipCourse';
import { renderDate, renderHoursAndMinutes } from '@/hooks/useDayjs';
import { useUserOptions } from '@/hooks/useApiOptions';

export interface StudentVipProps {}

const StudentVip: React.FC<StudentVipProps> = () => {
  // state
  const { studentId = '' } = useParams<any>();
  const { renderUserLabel } = useUserOptions();

  // api
  const getTableData = async (_: any, formData: any) => {
    const data = await getAllVipCourses({
      ...fixParams(null, formData),
      student: studentId,
    });
    return {
      total: data?.totalCount,
      list: (data?.vipCourses as API.VipCourse[]) || [],
    };
  };
  const vipCoursesAPI = useAntdTable(getTableData);

  return (
    <>
      <Table
        rowKey="_id"
        size="small"
        {...vipCoursesAPI.tableProps}
        pagination={false}
      >
        <Table.Column
          title="日期"
          dataIndex="vip_date"
          render={renderDate('MM/DD')}
        />
        <Table.Column
          title="讲师姓名"
          dataIndex="vip_teacher"
          render={renderUserLabel}
        />
        <Table.Column title="开始时间" dataIndex="start_date_str" />
        <Table.Column title="结束时间" dataIndex="end_date_str" />
        <Table.Column
          title="讲义时间"
          dataIndex="duration"
          render={renderHoursAndMinutes()}
        />
        <Table.Column title="备注" dataIndex="note" />
        <Table.Column
          title="年月"
          dataIndex="vip_date"
          render={renderDate('YYYYMM')}
        />
      </Table>
    </>
  );
};

export default StudentVip;
