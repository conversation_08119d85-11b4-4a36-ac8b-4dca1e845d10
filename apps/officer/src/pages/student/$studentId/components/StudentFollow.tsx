import { useParams } from 'umi';
import { useRequest } from 'ahooks';
import { Button, message, Modal, Table } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
////
import { useForm } from '@/hooks/useSKForm';
import { renderDate } from '@/hooks/useDayjs';
import {
  getStudentById,
  addFollowHistoryToStudent,
  updateFollowHistoryToStudent,
  removeFollowHistoryFromStudent,
} from '@/services/request/student';
import {
  fixFileListToUrl,
  fixUrlToFileList,
} from '@/pages/student/components/S3Upload';
import StudentFollowForm from '@/pages/student/components/StudentFollowForm';
import { Access, renderAccess, useAccess } from '@/hooks/useAccess';

export interface StudentFollowProps {}

const StudentFollow: React.FC<StudentFollowProps> = () => {
  // state
  const access = useAccess();
  const { formType, formProps, handleOpen } = useForm<API.FollowHistory>();
  const { studentId = '' } = useParams<any>();

  // api
  const studentAPI = useRequest(
    async () => await getStudentById({ studentId }),
    { cacheKey: 'studentById' },
  );

  // action
  const handleAdd = () => {
    handleOpen({
      title: '新建跟进信息',
      type: 'add',
      data: null,
    });
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await addFollowHistoryToStudent({
          studentId,
          ...v,
          files: fixFileListToUrl(v?.files || []),
        });
      }
      if (formType === 'edit') {
        if (!formProps.dataSource?._id) throw '';
        await updateFollowHistoryToStudent({
          studentId,
          followHistoryId: formProps.dataSource?._id,
          followHistory: {
            ...v,
            files: fixFileListToUrl(v?.files || []),
          },
        });
      }
      studentAPI.refresh();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  // render
  const RenderStudentFollowActions = renderAccess({
    accessible:
      access.page?.['studentFollow.D'] || access.page?.['studentFollow.U'],
    children: (
      <Table.ColumnGroup title="操作">
        {renderAccess({
          accessible: access.page?.['studentFollow.U'],
          children: (
            <Table.Column
              title="编辑"
              fixed="right"
              width={50}
              render={(row) => {
                const handleEdit = () => {
                  handleOpen({
                    title: '编辑跟进记录',
                    type: 'edit',
                    data: {
                      ...row,
                      files: fixUrlToFileList(row?.files || []),
                    },
                  });
                };
                return (
                  <Button size="small" onClick={handleEdit}>
                    <EditOutlined />
                  </Button>
                );
              }}
            />
          ),
        })}
        {renderAccess({
          accessible: access.page?.['studentFollow.D'],
          children: (
            <Table.Column
              title="删除"
              fixed="right"
              width={50}
              render={(row) => {
                const handleDelete = () => {
                  Modal.confirm({
                    centered: true,
                    title: '删除',
                    content: '确认删除本跟进记录，删除后无法恢复。',
                    onOk: async () => {
                      await removeFollowHistoryFromStudent({
                        studentId,
                        followHistoryId: row?._id,
                      });
                      await studentAPI.refreshAsync();
                    },
                    okText: '确认',
                    cancelText: '取消',
                  });
                };
                return (
                  <Button size="small" onClick={handleDelete}>
                    <DeleteOutlined />
                  </Button>
                );
              }}
            />
          ),
        })}
      </Table.ColumnGroup>
    ),
  });

  return (
    <>
      <StudentFollowForm
        type={formType}
        {...formProps}
        onSubmit={handleSubmit}
      />
      <Access accessible={access.page?.['studentFollow.C']}>
        <Button className="mb-4" type="primary" onClick={handleAdd}>
          新建
        </Button>
      </Access>
      <Table
        rowKey="_id"
        dataSource={studentAPI?.data?.follow_histories}
        size="small"
      >
        <Table.Column title="跟进类型" dataIndex="category" />
        <Table.Column title="跟进内容" dataIndex="content" />
        <Table.Column
          title="跟进时间"
          dataIndex="updatedAt"
          render={renderDate()}
        />
        <Table.Column
          title="跟进附件"
          dataIndex="files"
          render={(files) =>
            files?.[0] ? (
              <a target="_blank" href={files[0]}>
                link
              </a>
            ) : (
              <></>
            )
          }
        />
        {RenderStudentFollowActions}
      </Table>
    </>
  );
};

export default StudentFollow;
