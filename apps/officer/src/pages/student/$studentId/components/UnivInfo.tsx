import { Descriptions } from 'antd';
////
import { renderDate } from '@/hooks/useDayjs';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import { UnivStatusCategory } from '@/utils/formConsts';

export interface UnivInfoProps {
  dataSource: API.Student;
  canReadAll: Boolean;
}

const UnivInfo: React.FC<UnivInfoProps> = (props) => {
  if (props?.canReadAll) {
    return (
      <>
        <Descriptions size="small" bordered column={2}>
          <Descriptions.Item label="学生属性">
            {renderLabel(studentJSON.StudentType)(
              props.dataSource?.student_type,
            )}
          </Descriptions.Item>
          <Descriptions.Item label="学生姓名">
            {props.dataSource?.name}
          </Descriptions.Item>
          <Descriptions.Item label="微信号">
            {props.dataSource?.wechat}
          </Descriptions.Item>
          <Descriptions.Item label="毕业院校">
            {props.dataSource?.grad_univ}
          </Descriptions.Item>
          <Descriptions.Item label="毕业院校类别">
            {props.dataSource?.grad_univ_category}
          </Descriptions.Item>
          <Descriptions.Item label="专业">
            {props.dataSource?.grad_faculty}
          </Descriptions.Item>
          <Descriptions.Item label="绩点">
            {props.dataSource?.gpa_score}
          </Descriptions.Item>
          <Descriptions.Item label="现在状态" span={2}>
            {renderLabel(UnivStatusCategory)(
              props.dataSource?.current_univ_status,
            )}
          </Descriptions.Item>
          <Descriptions.Item label="志望院校">
            {props.dataSource?.goal_univ}
          </Descriptions.Item>
          <Descriptions.Item label="志望专业">
            {props.dataSource?.goal_faculty}
          </Descriptions.Item>
          <Descriptions.Item label="来日本的日期">
            {renderDate('YYYY-MM')(props.dataSource?.to_jp_date)}
          </Descriptions.Item>
          <Descriptions.Item label="语言学校">
            {props.dataSource?.lang_school}
          </Descriptions.Item>
          <Descriptions.Item label="英语成绩">
            {props.dataSource?.en_scores?.map((i: any, key: number) => (
              <div key={key}>{`${i?.category}: ${i?.score}`}</div>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="日语成绩">
            {props.dataSource?.ja_scores?.map((i: any, key: number) => (
              <div key={key}>{`${i?.category}: ${i?.score}`}</div>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="咨询备注" span={2}>
            {props.dataSource?.consult_note}
          </Descriptions.Item>
          <Descriptions.Item label="来源" span={2}>
            {props.dataSource?.source_from}
          </Descriptions.Item>
        </Descriptions>
      </>
    );
  } else
    return (
      <>
        <Descriptions size="small" bordered column={2}>
          <Descriptions.Item label="学生姓名">
            {props.dataSource?.name}
          </Descriptions.Item>
          <Descriptions.Item label="微信号">
            {props.dataSource?.wechat}
          </Descriptions.Item>
          <Descriptions.Item label="毕业院校">
            {props.dataSource?.grad_univ}
          </Descriptions.Item>
          <Descriptions.Item label="毕业院校类别">
            {props.dataSource?.grad_univ_category}
          </Descriptions.Item>
          <Descriptions.Item label="专业">
            {props.dataSource?.grad_faculty}
          </Descriptions.Item>
          <Descriptions.Item label="绩点">
            {props.dataSource?.gpa_score}
          </Descriptions.Item>
          <Descriptions.Item label="现在状态" span={2}>
            {renderLabel(UnivStatusCategory)(
              props.dataSource?.current_univ_status,
            )}
          </Descriptions.Item>
          <Descriptions.Item label="志望院校">
            {props.dataSource?.goal_univ}
          </Descriptions.Item>
          <Descriptions.Item label="志望专业">
            {props.dataSource?.goal_faculty}
          </Descriptions.Item>
          <Descriptions.Item label="来日本的日期">
            {renderDate('YYYY-MM')(props.dataSource?.to_jp_date)}
          </Descriptions.Item>
          <Descriptions.Item label="语言学校">
            {props.dataSource?.lang_school}
          </Descriptions.Item>
          <Descriptions.Item label="英语成绩">
            {props.dataSource?.en_scores?.map((i: any, key: number) => (
              <div key={key}>{`${i?.category}: ${i?.score}`}</div>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="日语成绩">
            {props.dataSource?.ja_scores?.map((i: any, key: number) => (
              <div key={key}>{`${i?.category}: ${i?.score}`}</div>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="咨询备注" span={2}>
            {props.dataSource?.consult_note}
          </Descriptions.Item>
          <Descriptions.Item label="来源" span={2}>
            {props.dataSource?.source_from}
          </Descriptions.Item>
        </Descriptions>
      </>
    );
};

export default UnivInfo;
