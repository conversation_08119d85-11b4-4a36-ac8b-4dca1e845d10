import { useParams } from 'umi';
import { useRequest } from 'ahooks';
import { Button, message, Modal, Table } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
////
import { useForm } from '@/hooks/useSKForm';
import {
  getStudentById,
  addPaymentHistoryToStudent,
  updatePaymentHistoryToStudent,
  removePaymentHistoryFromStudent,
} from '@/services/request/student';
import { renderDate } from '@/hooks/useDayjs';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import StudentPaymentForm from '@/pages/student/components/StudentPaymentForm';
import { useAccess, Access, renderAccess } from '@/hooks/useAccess';

export interface StudentPaymentProps {}

const StudentPayment: React.FC<StudentPaymentProps> = () => {
  // state
  const access = useAccess();
  const { formType, formProps, handleOpen } = useForm<API.PaymentHistory>();
  const { studentId = '' } = useParams<any>();

  // api
  const studentAPI = useRequest(
    async () => await getStudentById({ studentId }),
    { cacheKey: 'studentById' },
  );

  // action
  const handleAdd = () => {
    handleOpen({
      title: '新建支付记录',
      type: 'add',
      data: null,
    });
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await addPaymentHistoryToStudent({ studentId, ...v });
      }
      if (formType === 'edit') {
        if (!formProps.dataSource?._id) throw '';
        await updatePaymentHistoryToStudent({
          studentId,
          paymentHistoryId: formProps.dataSource?._id,
          paymentHistory: v,
        });
      }
      studentAPI.refresh();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  // render
  const RenderStudentPaymentActions = renderAccess({
    accessible:
      access.page?.['studentPayment.U'] || access.page?.['studentPayment.C'],
    children: (
      <Table.ColumnGroup title="操作">
        {renderAccess({
          accessible: access.page?.['studentPayment.U'],
          children: (
            <Table.Column
              title="编辑"
              fixed="right"
              width={50}
              render={(row) => {
                const handleEdit = () => {
                  handleOpen({
                    title: '编辑支付记录',
                    type: 'edit',
                    data: row,
                  });
                };
                return (
                  <Button size="small" onClick={handleEdit}>
                    <EditOutlined />
                  </Button>
                );
              }}
            />
          ),
        })}
        {renderAccess({
          accessible: access.page?.['studentPayment.D'],
          children: (
            <Table.Column
              title="删除"
              fixed="right"
              width={50}
              render={(row) => {
                const handleDelete = () => {
                  Modal.confirm({
                    centered: true,
                    title: '删除',
                    content: '确认删除本支付记录，删除后无法恢复。',
                    onOk: async () => {
                      await removePaymentHistoryFromStudent({
                        studentId,
                        paymentHistoryId: row?._id,
                      });
                      await studentAPI.refreshAsync();
                    },
                    okText: '确认',
                    cancelText: '取消',
                  });
                };
                return (
                  <Button size="small" onClick={handleDelete}>
                    <DeleteOutlined />
                  </Button>
                );
              }}
            />
          ),
        })}
      </Table.ColumnGroup>
    ),
  });

  return (
    <>
      <StudentPaymentForm
        type={formType}
        {...formProps}
        onSubmit={handleSubmit}
      />
      <Access accessible={access.page?.['studentPayment.C']}>
        <Button className="mb-4" type="primary" onClick={handleAdd}>
          新建
        </Button>
      </Access>
      <Table
        rowKey="_id"
        dataSource={studentAPI?.data?.payment_histories}
        size="small"
      >
        <Table.Column
          title="付款形式"
          dataIndex="type"
          render={renderLabel(studentJSON.paymentHistoryType)}
        />
        <Table.Column title="付款金额" dataIndex="amount" />
        <Table.Column title="付款方式" dataIndex="method" />
        <Table.Column
          title="付款完成日"
          dataIndex="date"
          render={renderDate('YYYY-MM-DD')}
        />
        <Table.Column title="备注" dataIndex="note" />
        {RenderStudentPaymentActions}
      </Table>
    </>
  );
};

export default StudentPayment;
