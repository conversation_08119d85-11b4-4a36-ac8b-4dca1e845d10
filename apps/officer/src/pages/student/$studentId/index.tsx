import { useNavigate, useParams } from 'umi';
import { useRequest } from 'ahooks';
import { Button, Descriptions, Space, Spin, Tabs, message } from 'antd';
////
import { dayjs, renderHoursAndMinutes } from '@/hooks/useDayjs';
import { useForm } from '@/hooks/useSKForm';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import { getStudentById, updateStudent } from '@/services/request/student';
import StudentInfoForm from '@/pages/student/components/StudentInfoForm';
import StudentInfo from './components/StudentInfo';
import StudentCourses from './components/StudentCourses';
import StudentPayment from './components/StudentPayment';
import StudentFollow from './components/StudentFollow';
import StudentVip from './components/StudentVip';
import { useUserOptions } from '@/hooks/useApiOptions';
import { useAccess, Access } from '@/hooks/useAccess';
import UnivInfo from './components/UnivInfo';
import GoalInfo from './components/GoalInfo';


const tabContentStyle = {
  height: 'calc( 100vh - 340px )',
};

const renderVipHours = renderHoursAndMinutes();

export interface GraduateStudentProps {}

const GraduateStudent: React.FC<GraduateStudentProps> = () => {
  // state
  const access = useAccess();
  const navigate = useNavigate();
  const { studentId = '' } = useParams<any>();
  const studentAPI = useRequest(
    async () => await getStudentById({ studentId }),
  );
  const { formType, formProps, handleOpen } = useForm<API.Student>();
  const { renderUserLabel } = useUserOptions();

  // 教务30天后可预览字段权限限制
  const unLimit =
    access.page?.['studentInfo.R.un_limit'] ||
    dayjs().isBefore(
      dayjs(studentAPI?.data?.payment_histories?.[0]?.date).add(30, 'day'),
    );
  const canReadAll = unLimit && !!access.page?.['studentInfo.R.full'];

  // data
  const vipTotalHours = studentAPI?.data?.vip_total_hours || 0;
  const vipUsedHours = studentAPI?.data?.vip_used_hours || 0;
  const vipLeftHours = vipTotalHours - vipUsedHours;

  // action
  const handleEditCommon = async () => {
    handleOpen({
      title: '修改课程管理信息',
      type: 'common',
      data: studentAPI?.data,
    });
  };
  const handleEdit = async () => {
    handleOpen({
      title: '编辑学生',
      type: 'edit',
      data: studentAPI?.data,
    });
  };
  const handleEditUnivInfo = async () => {
    handleOpen({
      title: '编辑考学信息',
      type: 'editUnivInfo',
      data: studentAPI?.data,
    });
  };
  const handleEditStudentInfo = async () => {
    handleOpen({
      title: '编辑身份信息',
      type: 'editStudentInfo',
      data: studentAPI?.data,
    });
  };

  const handleSubmit = async (v: any) => {
    try {
      if (formProps?.dataSource?._id) {
        await updateStudent({
          ...v,
          studentId: formProps?.dataSource?._id,
        });
      }
      studentAPI.run();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  return (
    <>
      <StudentInfoForm type={formType} {...formProps} onSubmit={handleSubmit} />
      <Descriptions
        title={studentAPI?.data?.name}
        extra={
          <Space>
            <Access accessible={access.page?.['studentInfo.UPDATE_COURSE']}>
              <Button type="primary" onClick={handleEditCommon}>
                修改课程管理信息
              </Button>
            </Access>
            <Button type="primary" onClick={handleEditUnivInfo}>
              编辑考学信息
            </Button>
            <Access accessible={access.page?.['studentInfo.D']}>
              <Button type="primary" onClick={handleEditStudentInfo}>
                编辑身份信息
              </Button>
            </Access>
            <Button onClick={() => navigate(-1)}>返回</Button>
          </Space>
        }
      >
        {/* <Descriptions.Item label="学生属性">
          {renderLabel(studentJSON.StudentType)(studentAPI?.data?.student_type)}
        </Descriptions.Item> */}
        <Descriptions.Item label="在学状态">
          {renderLabel(studentJSON.OnStatus)(studentAPI?.data?.on_status)}
        </Descriptions.Item>
        <Descriptions.Item label="班主任">
          {renderUserLabel(studentAPI?.data?.current_follow_teacher)}
        </Descriptions.Item>
        {studentAPI?.data?.is_vip_course ? (
          <>
            <Descriptions.Item label="VIP可用时长">
              {renderVipHours(vipTotalHours)}
            </Descriptions.Item>
            <Descriptions.Item label="VIP消耗时长">
              {renderVipHours(vipUsedHours)}
            </Descriptions.Item>
            <Descriptions.Item label="VIP剩余时长">
              {renderVipHours(vipLeftHours)}
            </Descriptions.Item>
          </>
        ) : (
          <Descriptions.Item label="有无VIP课程">无</Descriptions.Item>
        )}
      </Descriptions>
      <Tabs
        items={[
          {
            key: '1',
            label: `考学信息`,
            children: (
              <Spin spinning={studentAPI.loading}>
                <div className="overflow-auto" style={tabContentStyle}>
                  <UnivInfo
                    dataSource={studentAPI.data}
                    canReadAll={canReadAll}
                  />
                </div>
              </Spin>
            ),
          },
          {
            key: '2',
            label: `报名课程信息`,
            children: (
              <Spin spinning={false}>
                <div className="overflow-auto" style={tabContentStyle}>
                  <StudentCourses />
                </div>
              </Spin>
            ),
          },
          {
            key: '3',
            label: `身份信息`,
            disabled: !access.page?.['studentInfo.U'],
            children: (
              <Spin spinning={studentAPI.loading}>
                <div className="overflow-auto" style={tabContentStyle}>
                  <StudentInfo
                    dataSource={studentAPI.data}
                    canReadAll={canReadAll}
                  />
                </div>
              </Spin>
            ),
          },
          {
            key: '4',
            label: `升学信息`,
            disabled: !access.page?.['goalInfo.U'],
            children: (
              <Spin spinning={studentAPI.loading}>
                <div className="overflow-auto" style={tabContentStyle}>
                  <GoalInfo />
                </div>
              </Spin>
            ),
          },
          {
            key: '5',
            label: `付款信息`,
            disabled: !access.page?.['studentPayment.R'],
            children: (
              <Spin spinning={studentAPI.loading}>
                <div className="overflow-auto" style={tabContentStyle}>
                  <StudentPayment />
                </div>
              </Spin>
            ),
          },
          {
            key: '6',
            label: `后期跟进`,
            children: (
              <Spin spinning={studentAPI.loading}>
                <div className="overflow-auto" style={tabContentStyle}>
                  <StudentFollow />
                </div>
              </Spin>
            ),
          },
          {
            key: '7',
            label: `VIP记录`,
            children: (
              <Spin spinning={studentAPI.loading}>
                <div className="overflow-auto" style={tabContentStyle}>
                  <StudentVip />
                </div>
              </Spin>
            ),
          },
        ]}
      />
    </>
  );
};

export default GraduateStudent;
