import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  message,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  DatePicker,
} from 'antd';
import { EditOutlined, DeleteOutlined, SendOutlined } from '@ant-design/icons';
////
import { fixParams } from '@/utils/function';
import { useForm } from '@/hooks/useSKForm';
import { useUserInfo } from '@/hooks/useUserInfo';
import { renderDate } from '@/hooks/useDayjs';
import { useUserOptions } from '@/hooks/useApiOptions';
import {
  createStudent,
  deleteStudentById,
  getAllStudents,
  updateStudent,
} from '@/services/request/student';
import StudentForm from '@/pages/student/components/StudentInfoForm';

export interface GraduateCheckProps {}

const GraduateCheck: React.FC<GraduateCheckProps> = () => {
  // state
  const [form] = Form.useForm();
  const { formType, formProps, handleOpen } = useForm<API.Student>();
  const { userInfo } = useUserInfo();
  const { renderUserLabel } = useUserOptions();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllStudents({
      // sign_status_in: JSON.stringify([1]),
      ...fixParams(pageData, formData),
      // teacher: userInfo?._id,
    });
    return {
      total: data?.totalCount,
      list: (data?.students as API.Student[]) || [],
    };
  };
  const studentsAPI = useAntdTable(getTableData, { form });

  return (
    <>
      <StudentForm type={formType} {...formProps} />
      <Form form={form}>
        <Row justify="start" gutter={16}>
          <Col flex="300px">
            <Form.Item name="wechat_lk">
              <Input placeholder="微信号" />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={studentsAPI.search.submit}>
                  模糊搜索
                </Button>
                <Button onClick={studentsAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowKey="_id"
        size="small"
        {...studentsAPI.tableProps}
        pagination={{
          ...studentsAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ x: 800 }}
      >
        <Table.Column
          fixed="left"
          title="创建时间"
          width={180}
          dataIndex="createdAt"
          render={renderDate('YYYY.MM.DD HH:mm')}
        />
        <Table.Column<API.Student>
          sorter
          fixed="left"
          width={120}
          title="学⽣姓名"
          dataIndex="name"
        />
        <Table.Column title="微信号" dataIndex="wechat" />
        <Table.Column
          title="学生来源"
          dataIndex="source_from"
        />
        <Table.Column
          title="销售老师"
          dataIndex="sale_teacher1"
          render={renderUserLabel}
        />
      </Table>
    </>
  );
};

export default GraduateCheck;
