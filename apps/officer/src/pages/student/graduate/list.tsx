import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  Table,
  Row,
  Col,
  Button,
  Space,
  DatePicker,
  Select,
  message,
  Modal,
} from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
////
import { filterOption, fixParams } from '@/utils/function';
import { useForm } from '@/hooks/useSKForm';
import { renderDate, dayjs } from '@/hooks/useDayjs';
import { renderLabel, studentJSON } from '@/hooks/useJSON';
import { getAllStudents, updateStudent, deleteStudentById } from '@/services/request/student';
import StudentTeacherForm from '../components/StudentTeacherForm';
import { useUserOptions } from '@/hooks/useApiOptions';
import { useUserInfo } from '@/hooks/useUserInfo';

export interface GraduateListProps {}

const GraduateList: React.FC<GraduateListProps> = () => {
  // state
  const [form] = Form.useForm();
  const { formType, formProps, handleOpen } = useForm<API.Student>();
  const { userInfo } = useUserInfo();
  const { userOptions, renderUserLabel } = useUserOptions();

  // Dynamic status calculation based on course_expired_date
  const renderDynamicStatus = (record: API.Student) => {
    const courseExpiredDate = (record as any)?.course_expired_date;
    const originalStatus = record?.on_status;

    if (courseExpiredDate) {
      const today = dayjs();
      const expiredDate = dayjs(courseExpiredDate);
      // TODO: I may need to use UTC time here, but depends on the real user condition.
      const daysUntilExpiry = expiredDate.diff(today, 'day');

      // If expired (past due)
      if (daysUntilExpiry < 0) {
        return '到期';
      }
      // If expiring within 30 days
      else if (daysUntilExpiry >= 0 && daysUntilExpiry <= 30) {
        return '即将到期';
      }
    }

    // Return original status for other cases
    return renderLabel(studentJSON.OnStatus)(originalStatus);
  };
  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllStudents({
      sign_status_in: JSON.stringify([3]),
      ...fixParams(pageData, formData),
      teacher: userInfo?._id,
    });
    return {
      total: data?.totalCount,
      list: (data?.students as API.Student[]) || [],
    };
  };
  const studentsAPI = useAntdTable(getTableData, { form });

  const advanceSearchForm = (
    <Form form={form}>
      <Row justify="end" gutter={16}>
        <Col flex="150px">
          <Form.Item name="student_type">
            <Select
              placeholder="学生属性"
              options={studentJSON.StudentType}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="name_lk">
            <Input placeholder="学生姓名" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="wechat_lk">
            <Input placeholder="微信号" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="on_status_in">
            <Select
              placeholder="在学状态"
              options={studentJSON.OnStatus}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="280px">
          <Form.Item name="course_expired_date_range">
            <DatePicker.RangePicker
              placeholder={['课程有效期', '课程有效期']}
              allowEmpty={[true, true]}
            />
          </Form.Item>
        </Col>
        <Col flex="280px">
          <Form.Item name="sign_date_range">
            <DatePicker.RangePicker
              placeholder={['签约日期', '签约日期']}
              allowEmpty={[true, true]}
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="grad_univ_category">
            <Select
              placeholder="毕业院校类别"
              options={studentJSON.GradUnivCategory}
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="grad_univ_lk">
            <Input placeholder="毕业院校" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="gpa_score_lk">
            <Input placeholder="绩点" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="goal_univ_lk">
            <Input placeholder="志望院校" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="goal_faculty_lk">
            <Input placeholder="志望专业" />
          </Form.Item>
        </Col>
        <Col flex="280px">
          <Form.Item name="to_jp_date_range">
            <DatePicker.RangePicker
              placeholder={['来日日期', '来日日期']}
              allowEmpty={[true, true]}
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="lang_school_lk">
            <Input placeholder="语言学校" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="is_en_score">
            <Select
              placeholder="英语成绩"
              options={studentJSON.Boolean}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="is_ja_score">
            <Select
              placeholder="日语成绩"
              options={studentJSON.Boolean}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="courses.0.department_name_lk">
            <Input placeholder="报名学院" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="courses.0.vip_course_lk">
            <Input placeholder="VIP课程" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="courses.0.is_math_course_bool">
            <Select
              placeholder="数学课程"
              options={studentJSON.Boolean}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="courses.0.is_en_course_bool">
            <Select
              placeholder="英语课程"
              options={studentJSON.Boolean}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="courses.0.is_ja_course_bool">
            <Select
              placeholder="日语课程"
              options={studentJSON.Boolean}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="courses.0.custom_source_lk">
            <Input placeholder="学生来源" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="payment_histories.0.type_int">
            <Select
              placeholder="付款形式"
              options={studentJSON.paymentHistoryType}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="is_paid">
            <Select
              placeholder="分期已完成"
              options={studentJSON.IsPaid}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="current_follow_teacher">
            <Select
              allowClear
              showSearch
              placeholder="班主任"
              options={userOptions}
              filterOption={filterOption}
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="pass_univ_lk">
            <Input placeholder="合格院校" />
          </Form.Item>
        </Col>
        <Col>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={studentsAPI.search.submit}>
                搜索
              </Button>
              <Button onClick={studentsAPI.search.reset}>重置</Button>
              <Button type="link" onClick={studentsAPI.search.changeType}>
                Simple Search
              </Button>
            </Space>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );

  const searchForm = (
    <Form form={form}>
      <Row justify="end" gutter={16}>
        <Col flex="150px">
          <Form.Item name="student_type">
            <Select
              placeholder="学生属性"
              options={studentJSON.StudentType}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="name_lk">
            <Input placeholder="学生姓名" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="wechat_lk">
            <Input placeholder="微信号" />
          </Form.Item>
        </Col>
        <Col flex="150px">
          <Form.Item name="on_status_in">
            <Select
              placeholder="在学状态"
              options={studentJSON.OnStatus}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col flex="280px">
          <Form.Item name="course_expired_date_range">
            <DatePicker.RangePicker
              placeholder={['课程有效期', '课程有效期']}
              allowEmpty={[true, true]}
            />
          </Form.Item>
        </Col>
        <Col>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={studentsAPI.search.submit}>
                搜索
              </Button>
              <Button onClick={studentsAPI.search.reset}>重置</Button>
              <Button type="link" onClick={studentsAPI.search.changeType}>
                Advanced Search
              </Button>
            </Space>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );

  // action
  const handleSubmit = async (v: any) => {
    try {
      if (formProps.dataSource?._id && formType === 'edit') {
        await updateStudent({
          current_follow_teacher: v?.current_follow_teacher,
          studentId: formProps.dataSource._id,
        });
      }
      studentsAPI.search.submit();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  return (
    <>
      <StudentTeacherForm {...formProps} onSubmit={handleSubmit} />
      {studentsAPI.search.type === 'simple' ? searchForm : advanceSearchForm}
      <Table
        rowKey="_id"
        size="small"
        {...studentsAPI.tableProps}
        pagination={{
          ...studentsAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ x: 3000 }}
      >
        <Table.Column
          fixed="left"
          width={120}
          title="学生属性"
          dataIndex="student_type"
          render={renderLabel(studentJSON.StudentType)}
        />
        <Table.Column
          fixed="left"
          width={120}
          title="在学状态"
          dataIndex="on_status"
          render={(_, record: API.Student) => renderDynamicStatus(record)}
        />
        <Table.Column<API.Student>
          title="学⽣姓名"
          width={120}
          fixed="left"
          dataIndex="name"
          render={(_, row) => (
            <Link to={`/student/${row?._id}`}>{row?.name}</Link>
          )}
        />
        <Table.Column title="微信号" dataIndex="wechat" />
        <Table.Column
          title="课程有效期"
          dataIndex="course_expired_date"
          render={renderDate('YYYY-MM-DD')}
        />
        <Table.Column
          title="签约日期"
          dataIndex={['courses', 0, 'sign_date']}
          render={renderDate('YYYY-MM-DD')}
        />
        <Table.Column
          title="毕业院校类别"
          dataIndex="grad_univ_category"
          render={renderLabel(studentJSON.GradUnivCategory)}
        />
        <Table.Column title="毕业院校" dataIndex="grad_univ" />
        <Table.Column title="绩点" dataIndex="gpa_score" />
        <Table.Column title="志望院校" dataIndex="goal_univ" />
        <Table.Column title="志望专业" dataIndex="goal_faculty" />
        <Table.Column
          title="来日本的日期"
          dataIndex="to_jp_date"
          render={renderDate('YYYY-MM-DD')}
        />
        <Table.Column title="语言学校" dataIndex="lang_school" />
        <Table.Column
          title="英语成绩"
          render={(row) =>
            row?.en_scores?.map((i: any) => `${i?.category}: ${i?.score}`)
          }
        />
        <Table.Column
          title="日语成绩"
          render={(row) =>
            row?.jp_scores?.map((i: any) => `${i?.category}: ${i?.score}`)
          }
        />
        <Table.Column
          title="报名学院"
          dataIndex={['courses', 0, 'department_name']}
        />
        <Table.Column
          title="VIP课程"
          dataIndex={['courses', 0, 'vip_course']}
        />
        <Table.Column
          title="数学课程"
          dataIndex={['courses', 0, 'math_course_note']}
        />
        <Table.Column
          title="英语课程"
          dataIndex={['courses', 0, 'en_course_note']}
        />
        <Table.Column
          title="日语课程"
          dataIndex={['courses', 0, 'ja_course_note']}
        />
        <Table.Column
          title="学生来源"
          dataIndex={['courses', 0, 'custom_source']}
        />
        <Table.Column
          title="付款形式"
          dataIndex={['payment_histories', 0, 'type']}
          render={renderLabel(studentJSON.paymentHistoryType)}
        />
        <Table.Column
          title="分期付款已完成"
          dataIndex="is_paid"
          render={renderLabel(studentJSON.IsPaid)}
        />
        <Table.Column title="合格院校" dataIndex="pass_univ" />
        
        <Table.Column
          fixed="right"
          width={120}
          title="班主任"
          dataIndex="current_follow_teacher"
          render={renderUserLabel}
        />
        <Table.Column
          title="分配班主任"
          fixed="right"
          width={100}
          render={(row) => {
            const handleEdit = () => {
              handleOpen({
                title: `指定学生【${row?.name}】的班主任`,
                type: 'edit',
                data: row,
              });
            };
            return (
              <Button size="small" onClick={handleEdit}>
                <EditOutlined />
              </Button>
            );
          }}
        />
        <Table.Column
          title="删除"
          fixed="right"
          width={80}
          render={(row) => {
            const handleDelete = () => {
              Modal.confirm({
                centered: true,
                title: '删除学生',
                content: `确认删除学生【${row?.name}】，删除后无法恢复。`,
                onOk: async () => {
                  await deleteStudentById({
                    studentId: row?._id,
                  });
                  studentsAPI.search.submit();
                },
                okText: '确认',
                cancelText: '取消',
              });
            };
            return (
              <Button size="small" onClick={handleDelete} >
                <DeleteOutlined />
              </Button>
            );
          }}
        />
      </Table>
    </>
  );
};

export default GraduateList;
