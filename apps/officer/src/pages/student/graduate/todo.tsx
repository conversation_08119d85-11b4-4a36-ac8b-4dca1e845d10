import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  message,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  DatePicker,
  Select,
} from 'antd';
import { EditOutlined, SendOutlined, DeleteOutlined } from '@ant-design/icons';
////
import { fixParams } from '@/utils/function';
import { useForm } from '@/hooks/useSKForm';
import { renderDate } from '@/hooks/useDayjs';
import { useUserOptions } from '@/hooks/useApiOptions';
import { studentJSON, renderLabel } from '@/hooks/useJSON';
import {
  createStudent,
  getAllStudents,
  updateStudent,
  deleteStudentById,
} from '@/services/request/student';
import StudentForm from '@/pages/student/components/StudentInfoForm';
import { useUserInfo } from '@/hooks/useUserInfo';

export interface GraduateTodoProps {}

const GraduateTodo: React.FC<GraduateTodoProps> = () => {
  // state
  const [form] = Form.useForm();
  const { formType, formProps, handleOpen } = useForm<API.Student>();
  const { userInfo } = useUserInfo();
  const { renderUserLabel } = useUserOptions();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllStudents({
      sign_status_in: JSON.stringify([2]),
      ...fixParams(pageData, formData),
      teacher: userInfo?._id,
    });
    return {
      total: data?.totalCount,
      list: (data?.students as API.Student[]) || [],
    };
  };
  const studentsAPI = useAntdTable(getTableData, { form });

  // action
  const handleAdd = () => {
    handleOpen({
      title: '新建学生',
      type: 'add',
      data: null,
    });
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await createStudent({
          ...v,
          sign_status: 2,
        });
      }
      if (formType === 'edit') {
        await updateStudent({ ...v, studentId: formProps?.dataSource?._id });
      }
      studentsAPI.search.submit();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };
  return (
    <>
      <StudentForm type={formType} {...formProps} onSubmit={handleSubmit} />
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="150px">
            <Form.Item name="courses.0.department_name_lk">
              <Input placeholder="报名学院" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="courses.0.course_name_lk">
              <Input placeholder="报名课程" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="courses.0.custom_source_lk">
              <Input placeholder="学生来源" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="grad_univ_lk">
              <Input placeholder="毕业院校" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="lang_school_lk">
              <Input placeholder="语言学校" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="pass_univ_lk">
              <Input placeholder="合格院校" />
            </Form.Item>
          </Col>
          <Col flex="280px">
            <Form.Item name="createdAt_range">
              <DatePicker.RangePicker placeholder={['创建时间', '创建时间']} />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="on_status_in">
              <Select placeholder="在学状态" options={studentJSON.OnStatus} />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="name_lk">
              <Input placeholder="学生姓名" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="wechat_lk">
              <Input placeholder="微信号" />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={studentsAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={studentsAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Button className="mb-4" type="primary" onClick={handleAdd}>
        新建
      </Button>
      <Table
        rowKey="_id"
        size="small"
        {...studentsAPI.tableProps}
        pagination={{
          ...studentsAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ x: 2600 }}
      >
        <Table.Column
          fixed="left"
          title="创建时间"
          width={180}
          dataIndex="createdAt"
          render={renderDate('YYYY-MM-DD HH:mm')}
        />
        <Table.Column
          fixed="left"
          title="在学状态"
          width={120}
          dataIndex="on_status"
          render={renderLabel(studentJSON.OnStatus)}
        />
        <Table.Column
          fixed="left"
          width={120}
          title="学⽣姓名"
          render={(row) => <Link to={`/student/${row?._id}`}>{row?.name}</Link>}
        />
        <Table.Column title="微信号" dataIndex="wechat" />
        <Table.Column
          title="报名学院"
          dataIndex={['courses', 0, 'department_name']}
        />
        <Table.Column
          title="报名课程"
          dataIndex={['courses', 0, 'course_name']}
        />
        <Table.Column
          title="课程金额"
          dataIndex={['courses', 0, 'original_price']}
        />
        <Table.Column
          title="优惠金额"
          dataIndex={['courses', 0, 'off_price']}
        />
        <Table.Column
          title="实际缴费金额"
          dataIndex={['courses', 0, 'final_price']}
        />
        <Table.Column
          title="学生来源"
          dataIndex={['courses', 0, 'custom_source']}
        />
        <Table.Column title="毕业院校" dataIndex="grad_univ" />
        <Table.Column title="语言学校" dataIndex="lang_school" />
        <Table.Column title="合格院校" dataIndex="pass_univ" />
        <Table.Column
          title="销售老师"
          dataIndex="sale_teacher1"
          render={renderUserLabel}
        />
        <Table.ColumnGroup title="操作">
          <Table.Column
            title="提交"
            fixed="right"
            width={50}
            render={(row) => {
              const handleSubmit = () => {
                Modal.confirm({
                  centered: true,
                  title: '提交给上课老师',
                  content: '提交后，本学生进入上课阶段，本操作无法回退。',
                  onOk: async () => {
                    await updateStudent({
                      studentId: row?._id,
                      sign_status: 3,
                      on_status: 2,
                    });
                    studentsAPI.search.submit();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button
                  disabled={row?.sign_status === 3}
                  size="small"
                  onClick={handleSubmit}
                >
                  <SendOutlined />
                </Button>
              );
            }}
          />
          <Table.Column
            title="删除"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDelete = () => {
                Modal.confirm({
                  centered: true,
                  title: '删除学生',
                  content: '确认删除本学生，删除后无法恢复。',
                  onOk: async () => {
                    await deleteStudentById({
                      studentId: row?._id,
                    });
                    studentsAPI.search.submit();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button size="small" onClick={handleDelete}>
                  <DeleteOutlined />
                </Button>
              );
            }}
          />
          {/* <Table.Column
            title="编辑"
            fixed="right"
            width={50}
            render={(row) => {
              const handleEdit = () => {
                handleOpen({
                  title: '编辑学生基础信息',
                  type: 'edit',
                  data: row,
                });
              };
              return (
                <Button size="small" onClick={handleEdit}>
                  <EditOutlined />
                </Button>
              );
            }}
          /> */}
        </Table.ColumnGroup>
      </Table>
    </>
  );
};

export default GraduateTodo;
