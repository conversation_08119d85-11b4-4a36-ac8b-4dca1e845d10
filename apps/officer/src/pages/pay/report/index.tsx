import { useState } from 'react';
import { useAntdTable } from 'ahooks';
import { Button, DatePicker, message, Space, Table, Tag } from 'antd';
////
import {
  getAllSalaryMonths,
  summarySalaryMonth,
} from '@/services/request/salaryMonths';
import { userJSON } from '@/hooks/useJSON';
import { dayjs } from '@/hooks/useDayjs';
import { useAccess } from '@/hooks/useAccess';
import { useUserInfo } from '@/hooks/useUserInfo';
import { formatterEn } from '@/utils/function';
import InfoModel from './components/InfoModel';
import ExportPayRowXlsx from './components/ExportPayRowXlsx';
import ExportPayReportXlsx from './components/ExportPayReportXlsx';
import DownloadFiles from './components/DownloadFiles';

export interface PayReportProps {}

const PayReport: React.FC<PayReportProps> = () => {
  // status
  const [month, setMonth] = useState<dayjs.Dayjs>(dayjs());
  const { userInfo } = useUserInfo();
  const access = useAccess();

  // api
  const getTableData = async (_: any, formData: any) => {
    const data = await getAllSalaryMonths({
      year: month?.startOf('M').format('YYYY'),
      month: month?.startOf('M').format('M'),
      check_user: userInfo?._id,
      ...formData,
    });
    const list = (data?.salaryMonths || [])
      ?.map((item: API.SalaryMonth) => {
        const temp = access.isSuper ? item : item?.checkUserSalary?.[0];
        return {
          _id: item?._id,
          year: item?.year,
          month: item?.month,
          full_name_cn: item?.full_name_cn,
          account_type: item?.account_type,
          bank_name: item?.user?.bank_account?.bank_name,
          bank_branch_name: item?.user.bank_account?.bank_branch_name,
          account_no: item?.user.bank_account?.account_no,
          holder_name: item?.user.bank_account?.holder_name,
          payee_name: item?.user.bank_account?.payee_name,
          CN_ID_no: item?.user.bank_account?.CN_ID_no,
          check_user: temp?.check_user,
          user: item?.user?._id,
          department_amount: temp?.department_amount,
          amounts: temp?.department_amount?.reduce((res, d) => {
            const [id1, id2] = d?.department_ids;
            if (id1 && id2) {
              const key = id1 + '_' + id2;
              return {
                ...res,
                [id1]: (res?.[id1] || 0) + Math.floor(d?.total_amount),
                [key]: (res?.[key] || 0) + Math.floor(d?.total_amount),
              };
            } else return res;
          }, {}),
          base_amount: Math.floor(temp?.base_amount || 0),
          travel_fee: Math.floor(temp?.travel_fee || 0),
          total_amount: Math.floor(temp?.total_amount || 0),
        };
      })
      .sort((a: any, b: any) => {
        if (b?.account_type === a?.account_type) {
          return b?.total_amount - a?.total_amount;
        }
        return b?.account_type - a?.account_type;
      });
    return {
      total: data?.totalCount,
      list,
    };
  };
  const salaryMonthAPI = useAntdTable(getTableData, {
    ready: access.isSuper !== undefined,
  });

  // action
  const handleMonth = (v: dayjs.Dayjs | null) => {
    if (!v) return;
    setMonth(v);
    salaryMonthAPI.search.submit();
  };
  const handleSummary = async () => {
    try {
      const res = await summarySalaryMonth({
        check_user: userInfo?._id,
        check_date_start: month.startOf('M').format('YYYY-MM-DD'),
        check_date_end: month.endOf('M').format('YYYY-MM-DD'),
      });
      message.success(res?.message);
      salaryMonthAPI.search.submit();
    } catch (err: any) {
      message.error(err?.message);
    }
  };

  return (
    <>
      <Space className="mb-4">
        <DatePicker
          allowClear={false}
          value={month}
          size="large"
          onChange={handleMonth}
          picker="month"
          format="YYYY年MM月"
        />
        <Button type="primary" size="large" onClick={handleSummary}>
          更新统计
        </Button>
        <ExportPayReportXlsx
          month={month}
          dataSource={salaryMonthAPI.tableProps.dataSource}
        />
        <ExportPayRowXlsx month={month} />
        <DownloadFiles month={month} />
      </Space>
      <Table
        {...salaryMonthAPI.tableProps}
        scroll={{ y: 'calc(100vh - 280px)' }}
        size="small"
        rowKey="_id"
        pagination={false}
      >
        <Table.Column
          title="姓名"
          width={150}
          fixed="left"
          render={(row) => (
            <InfoModel dataSource={row} handleSummary={handleSummary} />
          )}
        />
        {access.isSuper && (
          <Table.ColumnGroup title="账户信息">
            <Table.Column
              title="工资收款账户类型"
              dataIndex="account_type"
              width={150}
              render={(v) => (
                <Tag color={userJSON.AccountType[v - 1].color}>
                  {userJSON.AccountType[v - 1].label}
                </Tag>
              )}
            />
            <Table.Column width={160} title="银行名称" dataIndex="bank_name" />
            <Table.Column
              width={160}
              title="支店名"
              dataIndex="bank_branch_name"
            />
            <Table.Column width={180} title="口座番号" dataIndex="account_no" />
            <Table.Column
              width={160}
              title="口座名义"
              dataIndex="holder_name"
            />
            <Table.Column
              width={160}
              title="收款人姓名"
              dataIndex="payee_name"
            />
            <Table.Column
              title="收款人二代居民身份证号"
              width={200}
              dataIndex="CN_ID_no"
            />
          </Table.ColumnGroup>
        )}
        <Table.Column
          fixed="right"
          title="基本给"
          width={150}
          dataIndex="base_amount"
          render={(v) => formatterEn(v)}
        />
        <Table.Column
          fixed="right"
          title="交通费"
          width={120}
          dataIndex="travel_fee"
          render={(v) => formatterEn(v)}
        />
        <Table.Column
          fixed="right"
          title="总金额"
          width={150}
          dataIndex="total_amount"
          render={(v) => formatterEn(v)}
        />
      </Table>
    </>
  );
};

export default PayReport;
