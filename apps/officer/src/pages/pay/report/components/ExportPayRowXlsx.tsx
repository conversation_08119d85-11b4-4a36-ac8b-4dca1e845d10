import * as XLSX from 'xlsx';
import { Button } from 'antd';
import { useRequest } from 'ahooks';
////
import { dayjs, renderDate } from '@/hooks/useDayjs';
import { useAccess } from '@/hooks/useAccess';
import { useUserInfo } from '@/hooks/useUserInfo';
import { JSONfind, renderLabel, salaryJSON } from '@/hooks/useJSON';
import { getAllSalaryRecords } from '@/services/request/salaryRecords';

export interface ExportPayRowXlsxProps {
  month: dayjs.Dayjs;
}

const renderRowDate = renderDate('YYYY-MM-DD');
const renderApplyType = renderLabel(salaryJSON.ApplyTypes);

const ExportPayRowXlsx: React.FC<ExportPayRowXlsxProps> = (props) => {
  // state
  const access = useAccess();
  const { userInfo } = useUserInfo();
  const check_user = access.isSuper ? undefined : userInfo._id;
  const applyDate = dayjs(props.month);

  // api
  const getTableData = async () => {
    const data = await getAllSalaryRecords({
      page: 0,
      perPage: 99999,
      apply_status_in: JSON.stringify([3]),
      check_user,
      apply_date_start: applyDate?.startOf('M').format('YYYY-MM-DD'),
      apply_date_end: applyDate?.endOf('M').format('YYYY-MM-DD'),
      sortField: 'department_ids',
      sortOrder: 1,
    });
    return {
      total: data?.totalCount,
      list: (data?.salaryRecords as API.SalaryRecord[]) || [],
    };
  };
  const salaryAPI = useRequest(getTableData, {
    manual: true,
    onSuccess: ({ list }) => {
      const department = ([id1, id2]: number[]) => {
        const d1 = JSONfind(salaryJSON.Departments)(id1);
        const d2 = JSONfind(d1?.children)(id2);
        return d1?.label ? `${d1?.label}/${d2?.label}` : '';
      };
      const fixExportData = list?.map((item) => ({
        日期: renderRowDate(item?.work_date),
        教师姓名: item?.user_fullname_cn,
        '所属部⻔': department(item?.department_ids),
        工作内容: item?.work_content,
        计费方式: renderApplyType(item?.apply_type),
        工作单价: item?.salary_per,
        '工作时间-开始时间': item?.start_time_str,
        '工作时间-结束时间': item?.end_time_str,
        '人/件/字数': item?.amount,
        '交通费-起始站': item?.travel_start,
        '交通费-终点站': item?.travel_end,
        '交通费-往返金额': item?.travel_fee,
        劳动时间: item?.work_hours,
        休息时间: item?.rest_hours,
        工作收入: item?.final_salary,
      }));

      let wb = XLSX.utils.book_new();
      let ws = XLSX.utils.json_to_sheet(fixExportData, { skipHeader: false });
      XLSX.utils.book_append_sheet(wb, ws, 'MIC');

      ws['!cols'] = Array.from(Object.keys(fixExportData[0]), () => ({
        wch: 20,
      }));

      const s2ab = (s: any) => {
        // 字符串转字符流
        let buf = new ArrayBuffer(s.length);
        let view = new Uint8Array(buf);
        for (let i = 0; i !== s.length; ++i) {
          view[i] = s.charCodeAt(i) & 0xff;
        }
        return buf;
      };
      // 创建二进制对象写入转换好的字节流
      let tmpDown = new Blob(
        [
          s2ab(
            XLSX.write(wb, {
              bookType: 'xlsx',
              bookSST: false,
              type: 'binary',
            }),
          ),
        ],
        { type: '' },
      );
      let a = document.createElement('a');
      // 利用URL.createObjectURL()方法为a元素生成blob URL
      a.href = URL.createObjectURL(tmpDown); // 创建对象超链接
      a.download = `${applyDate?.format('YYYY年MM月申报数据')}.xls`;
      a.click();
    },
  });

  return (
    <Button
      size="large"
      loading={salaryAPI.loading}
      onClick={() => salaryAPI.run()}
    >
      导出申报数据
    </Button>
  );
};

export default ExportPayRowXlsx;
