import * as XLSX from 'xlsx';
import { Button } from 'antd';
////
import { dayjs } from '@/hooks/useDayjs';
import { userJSON } from '@/hooks/useJSON';
import { useAccess } from '@/hooks/useAccess';

export interface ExportPayReportXlsxProps {
  month: dayjs.Dayjs;
  dataSource: any[];
}

const ExportPayReportXlsx: React.FC<ExportPayReportXlsxProps> = (props) => {
  const access = useAccess();

  const handleExport = () => {
    // 修正数据格式
    const totalRecords = props?.dataSource?.reduce((res: any, item: any) => {
      let temp = {
        full_name_cn: '合计',
        amounts: res?.amounts || {},
        base_amount: (res?.base_amount || 0) + item?.base_amount,
        travel_fee: (res?.travel_fee || 0) + item?.travel_fee,
        total_amount: (res?.total_amount || 0) + item?.total_amount,
      };
      for (const key in item?.amounts) {
        if (Object.prototype.hasOwnProperty.call(item?.amounts, key)) {
          const element = item?.amounts[key];
          temp.amounts[key] = (temp.amounts[key] || 0) + element;
        }
      }
      return temp;
    }, {});

    const data = [...(props?.dataSource || []), totalRecords];

    const fixExportData = data?.map((item) => ({
      姓名: item?.full_name_cn,
      ...(access.isSuper
        ? {
            工资收款账户类型:
              userJSON.AccountType?.[item?.account_type - 1]?.label,
            银行名称: item?.bank_name,
            支店名: item?.bank_branch_name,
            口座番号: item?.account_no,
            口座名义: item?.holder_name,
            收款人姓名: item?.payee_name,
            收款人二代居民身份证号: item?.CN_ID_no,
          }
        : {}),
      '学部-班主任': item?.amounts?.['1_1'],
      '学部-大课讲师': item?.amounts?.['1_2'],
      '学部-vip讲师': item?.amounts?.['1_3'],
      '学部-备课/过去问制作': item?.amounts?.['1_4'],
      '学部-试讲': item?.amounts?.['1_5'],
      '学部-其他': item?.amounts?.['1_6'],
      '学部-正社员': item?.amounts?.['1_7'],
      // '学部-合计': item?.amounts?.['1'],

      '大学院-班主任': item?.amounts?.['2_1'],
      '大学院-大课讲师': item?.amounts?.['2_2'],
      '大学院-vip讲师': item?.amounts?.['2_3'],
      '大学院-文书修改': item?.amounts?.['2_4'],
      '大学院-备课/过去问制作': item?.amounts?.['2_5'],
      '大学院-试讲': item?.amounts?.['2_6'],
      '大学院-其他': item?.amounts?.['2_7'],
      '大学院-正社员': item?.amounts?.['2_8'],
      '大学院-规划组': item?.amounts?.['2_9'],
      '大学院-专业小组长': item?.amounts?.['2_10'],
      // '大学院-合计': item?.amounts?.['2'],

      '市场营销-宣传': item?.amounts?.['3_1'],
      '市场营销-业务拓展': item?.amounts?.['3_2'],
      '市场营销-其他': item?.amounts?.['3_3'],
      '市场营销-正社员': item?.amounts?.['3_4'],
      // '市场营销-合计': item?.amounts?.['3'],

      '业务服务-教务': item?.amounts?.['4_1'],
      '业务服务-其他': item?.amounts?.['4_2'],
      '业务服务-正社员': item?.amounts?.['4_3'],
      // '业务服务-合计': item?.amounts?.['4'],

      '语学类-托福/托业': item?.amounts?.['5_1'],
      '语学类-日语': item?.amounts?.['5_2'],
      '语学类-日语口语': item?.amounts?.['5_3'],
      '语学类-模拟面试': item?.amounts?.['5_4'],
      '语学类-其他': item?.amounts?.['5_5'],
      // '语学类-合计': item?.amounts?.['5'],

      '其他-其他(慎点)': item?.amounts?.['6_1'],
      基本给: item?.base_amount,
      交通费: item?.travel_fee,
      总金额: item?.total_amount,
    }));

    let wb = XLSX.utils.book_new();
    let ws = XLSX.utils.json_to_sheet(fixExportData, { skipHeader: false });
    XLSX.utils.book_append_sheet(wb, ws, 'MIC');

    ws['!cols'] = Array.from(Object.keys(fixExportData[0]), () => ({
      wch: 20,
    }));

    const s2ab = (s: any) => {
      // 字符串转字符流
      let buf = new ArrayBuffer(s.length);
      let view = new Uint8Array(buf);
      for (let i = 0; i !== s.length; ++i) {
        view[i] = s.charCodeAt(i) & 0xff;
      }
      return buf;
    };
    // 创建二进制对象写入转换好的字节流
    let tmpDown = new Blob(
      [
        s2ab(
          XLSX.write(wb, {
            bookType: 'xlsx',
            bookSST: false,
            type: 'binary',
          }),
        ),
      ],
      { type: '' },
    );
    let a = document.createElement('a');
    // 利用URL.createObjectURL()方法为a元素生成blob URL
    a.href = URL.createObjectURL(tmpDown); // 创建对象超链接
    a.download = `${props?.month?.format('YYYY年MM月工资报表')}.xls`;
    a.click();
  };

  return (
    <Button size="large" onClick={handleExport}>
      导出工资报表
    </Button>
  );
};

export default ExportPayReportXlsx;
