import { Button } from 'antd';
////
import { dayjs } from '@/hooks/useDayjs';
import { downloadFiles } from '@/services/useAWS_S3';

export interface DownloadFilesProps {
  month: dayjs.Dayjs;
}

const DownloadFiles: React.FC<DownloadFilesProps> = (props) => {
  const monthUrl = dayjs(props.month).format('YYYYMM');
  const handleDownload = () => {
    downloadFiles('salary_file_links/' + monthUrl);
  };
  return (
    <Button size="large" onClick={handleDownload}>
      下载备课文件
    </Button>
  );
};

export default DownloadFiles;
