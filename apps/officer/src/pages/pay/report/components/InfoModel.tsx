import { useAntdTable, useBoolean } from 'ahooks';
import { Button, Descriptions, Divider, Modal, Space, Table } from 'antd';
////
import { formatterEn } from '@/utils/function';
import { dayjs, renderDate, renderHours } from '@/hooks/useDayjs';
import { JSONfind, renderLabel, salaryJSON } from '@/hooks/useJSON';
import { useUserOptions } from '@/hooks/useApiOptions';
import { getAllSalaryRecords } from '@/services/request/salaryRecords';

export interface InfoModelProps {
  dataSource: API.SalaryMonth;
  handleSummary: () => void;
}

const InfoModel: React.FC<InfoModelProps> = (props) => {
  // state
  const [open, { setFalse, setTrue }] = useBoolean(false);
  const { renderUserLabel } = useUserOptions();
  const { year, month, full_name_cn } = props.dataSource;

  const applyDate = dayjs(year + '/' + month);
  const workDate = dayjs(year + '/' + month).add(-1, 'M');
  const title = `【${full_name_cn}】 ${workDate?.format(
    'YYYY-MM',
  )} 工资审核详情`;

  // api
  const getTableData = async (pageData: any, formData: any) => {
    // const page = pageData.current - 1;
    // const perPage = pageData.pageSize;
    let sorter: any = {};
    if (Array.isArray(pageData?.sorter?.field)) {
      sorter.sortField = pageData?.sorter?.field?.join('.');
    } else if (!!pageData?.sorter?.field) {
      sorter.sortField = pageData?.sorter?.field;
    } else {
      sorter.sortField = 'work_date';
    }
    if (pageData?.sorter?.order === 'ascend') {
      sorter.sortOrder = 1;
    }
    if (pageData?.sorter?.order === 'descend') {
      sorter.sortOrder = -1;
    }
    const data = await getAllSalaryRecords({
      page: 0,
      perPage: 99999,
      apply_status_in: JSON.stringify([3]),
      check_user: props.dataSource.check_user,
      user: props.dataSource.user,
      apply_date_start: applyDate?.startOf('M').format('YYYY-MM-DD'),
      apply_date_end: applyDate?.endOf('M').format('YYYY-MM-DD'),
      // work_date_start: workDate?.startOf('M').format('YYYY-MM-DD'),
      // work_date_end: workDate?.endOf('M').format('YYYY-MM-DD'),
      ...formData,
      ...sorter,
    });
    return {
      total: data?.totalCount,
      list: ((data?.salaryRecords as API.SalaryRecord[]) || [])?.map(
        (item) => ({
          ...item,
          travel_fee: Math.floor(item?.travel_fee || 0),
          final_salary: Math.floor(item?.final_salary || 0),
        }),
      ),
    };
  };
  const salaryAPI = useAntdTable(getTableData, {
    manual: true,
  });

  const handleOpen = () => {
    salaryAPI.search.submit();
    setTrue();
  };

  const [finalSalary, workHours, restHours, travelFee, amount] =
    salaryAPI?.data?.list?.reduce(
      (temp, item) => {
        temp[0] = parseFloat((temp[0] + item.final_salary || 0).toFixed(6));
        temp[1] = parseFloat((temp[1] + item.work_hours || 0).toFixed(6));
        temp[2] = parseFloat((temp[2] + item.rest_hours || 0).toFixed(6));
        temp[3] = parseFloat((temp[3] + item.travel_fee || 0).toFixed(6));
        temp[4] = parseFloat((temp[4] + item.amount || 0).toFixed(6));
        return temp;
      },
      [0, 0, 0, 0, 0],
    ) || [0, 0, 0, 0, 0];

  return (
    <>
      <Modal
        width={1200}
        title={title}
        open={open}
        onOk={setFalse}
        onCancel={setFalse}
        maskClosable={false}
      >
        <Descriptions
          size="small"
          title="工资统计"
          className="m-6"
          bordered
          extra={
            <Button type="primary" onClick={props.handleSummary}>
              更新统计
            </Button>
          }
        >
          <Descriptions.Item label="基本给">
            {formatterEn(props.dataSource.base_amount)}
          </Descriptions.Item>
          <Descriptions.Item label="交通费">
            {formatterEn(props.dataSource.travel_fee)}
          </Descriptions.Item>
          <Descriptions.Item label="总金额">
            {formatterEn(props.dataSource.total_amount)}
          </Descriptions.Item>
        </Descriptions>
        <Table
          size="small"
          className="m-6"
          rowKey="_id"
          bordered
          {...salaryAPI.tableProps}
          scroll={{ x: 2300, y: 'calc( 100vh - 480px )' }}
          pagination={false}
          footer={() => (
            <Space split={<Divider type="vertical" />}>
              <span>总件数：{amount}</span>
              <span>总劳动时间：{renderHours(2)(workHours)}</span>
              <span>总休息时间：{renderHours(2)(restHours)}</span>
              <span>总带薪时间：{renderHours(2)(workHours - restHours)}</span>
              <span>总交通费：{formatterEn(travelFee)}</span>
              <span>总工作收入：{formatterEn(finalSalary)}</span>
            </Space>
          )}
        >
          <Table.Column
            title="日期"
            fixed="left"
            width={110}
            sorter
            dataIndex="work_date"
            render={renderDate('YYYY-MM-DD')}
          />
          <Table.Column
            title="所属部⻔"
            width={160}
            sorter
            dataIndex="department_ids"
            render={([id1, id2]) => {
              const d1 = JSONfind(salaryJSON.Departments)(id1);
              const d2 = JSONfind(d1?.children)(id2);
              return d1?.label ? `${d1?.label}/${d2?.label}` : '';
            }}
          />
          <Table.Column width={160} title="工作内容" dataIndex="work_content" />
          <Table.Column
            title="计费方式"
            width={100}
            dataIndex="apply_type"
            render={renderLabel(salaryJSON.ApplyTypes)}
          />
          <Table.Column
            title="工作单价"
            width={100}
            dataIndex="salary_per"
            render={(v) => formatterEn(v)}
          />
          <Table.ColumnGroup title="工作时间">
            <Table.Column
              title="开始时间"
              width={100}
              dataIndex="start_time_str"
            />
            <Table.Column
              title="结束时间"
              width={100}
              dataIndex="end_time_str"
            />
          </Table.ColumnGroup>
          <Table.Column width={100} title="人/件/字数" dataIndex="amount" />
          <Table.ColumnGroup title="交通费">
            <Table.Column width={100} title="起始站" dataIndex="travel_start" />
            <Table.Column width={100} title="终点站" dataIndex="travel_end" />
            <Table.Column
              title="往返金额"
              width={100}
              dataIndex="travel_fee"
              render={(v) => formatterEn(v)}
            />
          </Table.ColumnGroup>
          <Table.Column
            width={100}
            title="劳动时间"
            dataIndex="work_hours"
            render={renderHours(2)}
          />
          <Table.Column
            width={100}
            title="休息时间"
            dataIndex="rest_hours"
            render={renderHours(2)}
          />
          <Table.Column title="备注" dataIndex="memo" />
          <Table.ColumnGroup title="审核人员">
            <Table.Column
              title="负责人"
              fixed="right"
              width={80}
              dataIndex="check_user"
              render={renderUserLabel}
            />
            <Table.Column
              title="小助手"
              fixed="right"
              width={80}
              dataIndex="sub_check_user"
              render={renderUserLabel}
            />
          </Table.ColumnGroup>
          <Table.Column
            fixed="right"
            title="工作收入"
            width={160}
            dataIndex="final_salary"
            render={(v) => formatterEn(v)}
          />
        </Table>
      </Modal>
      <Button size="small" type="link" onClick={handleOpen}>
        {full_name_cn}
      </Button>
    </>
  );
};

export default InfoModel;
