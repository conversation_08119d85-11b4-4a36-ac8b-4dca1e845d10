import { useBoolean } from 'ahooks';
import { Button, Checkbox, Descriptions, Modal, Tag } from 'antd';
////
import { formatterEn } from '@/utils/function';
import { dayjs, renderHours } from '@/hooks/useDayjs';
import { JSONfind, salaryJSON } from '@/hooks/useJSON';
import { useUserOptions } from '@/hooks/useApiOptions';
import { updateSalaryRecord } from '@/services/request/salaryRecords';

export interface InfoModelProps {
  dataSource: API.SalaryRecord;
  refetch: () => void;
}

const InfoModel: React.FC<InfoModelProps> = (props) => {
  // state
  const [open, { setFalse, setTrue }] = useBoolean(false);
  const { userOptions } = useUserOptions();
  const {
    apply_type,
    apply_status = 2,
    check_user,
    department_ids,
    sub_check_user,
    sub_check_status = 1,
    user_fullname_cn,
    work_date,
  } = props.dataSource;

  // data
  const workDate = dayjs(work_date)?.format('YYYY-MM-DD');
  const title = `【${user_fullname_cn}】 ${workDate} 工作申报`;
  const checkUser = JSONfind(userOptions)(check_user);
  const subCheckUser = JSONfind(userOptions)(sub_check_user);
  const applyStatus = salaryJSON.ApplyStatus[apply_status - 1];
  const subCheckStatus = salaryJSON.SubCheckStatus[sub_check_status - 1];
  const d1 = JSONfind(salaryJSON.Departments)(department_ids?.[0]);
  const d2 = JSONfind(d1?.children)(department_ids?.[1]);
  const applyType = JSONfind(salaryJSON.ApplyTypes)(apply_type);

  // action
  const handleChange = async (check_status: number) => {
    if (!props.dataSource._id) return;
    await updateSalaryRecord({
      salaryRecordId: props.dataSource._id,
      check_status,
    });
    props?.refetch?.();
  };
  const handleChange2 = () => handleChange(2); // 待定
  const handleChange3 = () => handleChange(3); // 通过
  const handleChange4 = () => handleChange(4); // 驳回

  return (
    <>
      <Modal
        width={1000}
        title={title}
        open={open}
        onOk={setFalse}
        onCancel={setFalse}
        maskClosable={false}
      >
        <Descriptions
          size="small"
          title="审核详情"
          className="m-6"
          bordered
          column={2}
        >
          <Descriptions.Item label="申报状态">
            <Tag color={applyStatus?.color}>{applyStatus?.label}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="⼯作负责人">
            {checkUser?.label}
          </Descriptions.Item>
          <Descriptions.Item label="转让审核状态">
            {subCheckStatus?.label || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="小助手姓名">
            {subCheckUser?.label || '-'}
          </Descriptions.Item>
        </Descriptions>
        <Descriptions
          size="small"
          title="申报详情"
          className="m-6"
          bordered
          column={2}
        >
          <Descriptions.Item label="工作收入" span={2}>
            {formatterEn(props.dataSource.final_salary)}
          </Descriptions.Item>
          <Descriptions.Item label="⼯作内容" span={2}>
            {props.dataSource.work_content}
          </Descriptions.Item>
          <Descriptions.Item label="工作单价">
            {formatterEn(props.dataSource.salary_per)}
          </Descriptions.Item>
          <Descriptions.Item label="人/件/字数">
            {props.dataSource.amount || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="⼯作所属部⻔">
            {`${d1?.label}/${d2?.label}`}
          </Descriptions.Item>
          <Descriptions.Item label="计费⽅式">
            {applyType?.label}
          </Descriptions.Item>
          <Descriptions.Item label="工作时间" span={2}>
            {props.dataSource.start_time_str}
            {applyType?.value === 1 ? '~' : '-'}
            {props.dataSource.end_time_str}
          </Descriptions.Item>
          <Descriptions.Item label="劳动时间">
            {renderHours(2)(props.dataSource.work_hours || 0)}
          </Descriptions.Item>
          <Descriptions.Item label="休息时间">
            {renderHours(2)(props.dataSource.rest_hours || 0)}
          </Descriptions.Item>
          <Descriptions.Item label="交通起始站">
            {props.dataSource.travel_start || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="交通终点站">
            {props.dataSource.travel_end || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="往返金额" span={2}>
            {formatterEn(props.dataSource.travel_fee)}
          </Descriptions.Item>
          <Descriptions.Item label="附件" span={2}>
            {props.dataSource.file_links?.map((url, key) => (
              <Button key={key} type="link" onClick={() => window.open(url)}>
                {url?.substring?.(url?.lastIndexOf?.('/') + 1)}
              </Button>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="备注" span={2}>
            {props.dataSource.memo || '-'}
          </Descriptions.Item>
        </Descriptions>
        <Descriptions size="small" title="审核操作" className="m-6" bordered>
          <Descriptions.Item label="通过">
            <Checkbox
              checked={props.dataSource.check_status === 3}
              onClick={handleChange3}
            />
          </Descriptions.Item>
          <Descriptions.Item label="驳回">
            <Checkbox
              checked={props.dataSource.check_status === 4}
              onClick={handleChange4}
            />
          </Descriptions.Item>
          <Descriptions.Item label="待定">
            <Checkbox
              checked={props.dataSource.check_status === 2}
              onClick={handleChange2}
            />
          </Descriptions.Item>
        </Descriptions>
      </Modal>
      <Button size="small" type="link" onClick={setTrue}>
        {workDate}
      </Button>
    </>
  );
};

export default InfoModel;
