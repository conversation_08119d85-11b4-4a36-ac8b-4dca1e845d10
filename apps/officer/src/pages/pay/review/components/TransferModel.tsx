import { useState } from 'react';
import { useBoolean } from 'ahooks';
import { Modal, Button, Select, Space, message } from 'antd';
import { filterOption } from '@/utils/function';

export interface TransferModelProps {
  disabled: boolean;
  dataSource: any[];
  handleTransfer: (v: any) => void;
}

const TransferModel: React.FC<TransferModelProps> = (props) => {
  // state
  const [open, { setFalse, setTrue }] = useBoolean(false);
  const [subCheckUser, setSubCheckUser] = useState();

  // action
  const handleOK = async () => {
    if (!subCheckUser) return message.warning('请选择小助手');
    props.handleTransfer(subCheckUser);
    setFalse();
  };

  return (
    <>
      <Button
        size="small"
        type="dashed"
        disabled={props?.disabled}
        onClick={setTrue}
      >
        转让小助手
      </Button>
      <Modal
        centered
        title="转让"
        open={open}
        onOk={handleOK}
        onCancel={setFalse}
        maskClosable={false}
      >
        <Space>
          指定审批小助手:
          <Select
            showSearch
            className="w-56"
            options={props?.dataSource}
            value={subCheckUser}
            filterOption={filterOption}
            onChange={(v) => setSubCheckUser(v)}
          />
        </Space>
      </Modal>
    </>
  );
};

export default TransferModel;
