import { useState } from 'react';
import { useParams } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Table,
  Tag,
  Checkbox,
  Button,
  Form,
  Row,
  Col,
  Space,
  Select,
  Divider,
  Cascader,
} from 'antd';
import type { TableRowSelection } from 'antd/lib/table/interface';
////
import { filterOption, formatterEn } from '@/utils/function';
import {
  getAllSalaryRecords,
  updateSalaryRecord,
} from '@/services/request/salaryRecords';
import { dayjs, renderHours } from '@/hooks/useDayjs';
import { useUserInfo } from '@/hooks/useUserInfo';
import { useUserOptions } from '@/hooks/useApiOptions';
import { JSONfind, renderLabel, salaryJSON } from '@/hooks/useJSON';
import InfoModel from './components/InfoModel';

export interface ReviewWithTimeProps {}

const ReviewWithTime: React.FC<ReviewWithTimeProps> = () => {
  // status
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const { workDate } = useParams();
  const { userInfo } = useUserInfo();
  const { userOptions } = useUserOptions();
  const [form] = Form.useForm();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    // const page = pageData.current - 1;
    // const perPage = pageData.pageSize;
    let sorter: any = {};
    if (Array.isArray(pageData?.sorter?.field)) {
      sorter.sortField = pageData?.sorter?.field?.join('.');
    } else if (!!pageData?.sorter?.field) {
      sorter.sortField = pageData?.sorter?.field;
    } else {
      sorter.sortField = 'work_date';
    }
    if (pageData?.sorter?.order === 'ascend') {
      sorter.sortOrder = 1;
    }
    if (pageData?.sorter?.order === 'descend') {
      sorter.sortOrder = -1;
    }
    const data = await getAllSalaryRecords({
      page: 0,
      perPage: 99999,
      apply_status_in: formData?.apply_status
        ? JSON.stringify([formData?.apply_status])
        : JSON.stringify([2, 3, 4]),
      sub_check_user: userInfo?._id,
      apply_date_start: dayjs()?.startOf('M').format('YYYY-MM-DD'),
      apply_date_end: dayjs()?.endOf('M').format('YYYY-MM-DD'),
      work_date_start: dayjs(workDate)?.startOf('M').format('YYYY-MM-DD'),
      work_date_end: dayjs(workDate)?.endOf('M').format('YYYY-MM-DD'),
      ...formData,
      ...sorter,
    });
    return {
      total: data?.totalCount,
      list: ((data?.salaryRecords as API.SalaryRecord[]) || [])?.map(
        (item) => ({
          ...item,
          travel_fee: Math.floor(item?.travel_fee || 0),
          final_salary: Math.floor(item?.final_salary || 0),
        }),
      ),
    };
  };
  const salaryAPI = useAntdTable(getTableData, { form });

  // action
  const handleClear = () => {
    setSelectedRowKeys([]);
  };
  const handleCheckStatus = async (sub_check_status: number) => {
    for await (const salaryRecordId of selectedRowKeys) {
      await updateSalaryRecord({ salaryRecordId, sub_check_status });
    }
    salaryAPI.search.submit();
  };

  // format
  const rowSelection: TableRowSelection<API.SalaryRecord> = {
    type: 'checkbox',
    fixed: true,
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange: (keys: any[]) => {
      setSelectedRowKeys(keys);
    },
  };
  const selected = selectedRowKeys?.length || 0;
  const [finalSalary, workHours, restHours, travelFee, amount] =
    salaryAPI?.data?.list?.reduce(
      (temp, item) => {
        temp[0] = parseFloat((temp[0] + item.final_salary || 0).toFixed(6));
        temp[1] = parseFloat((temp[1] + item.work_hours || 0).toFixed(6));
        temp[2] = parseFloat((temp[2] + item.rest_hours || 0).toFixed(6));
        temp[3] = parseFloat((temp[3] + item.travel_fee || 0).toFixed(6));
        temp[4] = parseFloat((temp[4] + item.amount || 0).toFixed(6));
        return temp;
      },
      [0, 0, 0, 0, 0],
    ) || [0, 0, 0, 0, 0];

  return (
    <>
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="140px">
            <Form.Item name="apply_status">
              <Select
                placeholder="申报状态"
                options={salaryJSON.ApplyStatus}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="180px">
            <Form.Item name="user">
              <Select
                allowClear
                showSearch
                placeholder="教师姓名"
                options={userOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Col>
          <Col flex="200px">
            <Form.Item name="department_ids">
              <Cascader
                placeholder="所属部⻔"
                options={salaryJSON.Departments}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="140px">
            <Form.Item name="apply_type">
              <Select
                placeholder="计费方式"
                options={salaryJSON.ApplyTypes}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col flex="140px">
            <Form.Item name="check_user">
              <Select
                allowClear
                showSearch
                placeholder="负责人"
                options={userOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Col>
          <Col>
            <Space>
              <Button type="primary" onClick={salaryAPI.search.submit}>
                搜索
              </Button>
              <Button onClick={salaryAPI.search.reset}>重置</Button>
            </Space>
          </Col>
        </Row>
      </Form>

      <Row>
        <Col>
          <Space>
            <span>已选: {selected} 项</span>
            <Button size="small" type="link" onClick={handleClear}>
              清除已选
            </Button>
          </Space>
        </Col>
        <Col flex="auto"></Col>
        <Col>
          <Space>
            <span>批量操作：</span>
            <Button
              size="small"
              type="dashed"
              disabled={!selected}
              onClick={() => handleCheckStatus(3)}
            >
              通过
            </Button>
            <Button
              size="small"
              type="dashed"
              disabled={!selected}
              onClick={() => handleCheckStatus(4)}
            >
              驳回
            </Button>
            <Button
              size="small"
              type="dashed"
              disabled={!selected}
              onClick={() => handleCheckStatus(2)}
            >
              待定
            </Button>
          </Space>
        </Col>
      </Row>
      <Table
        {...salaryAPI.tableProps}
        footer={() => (
          <Space split={<Divider type="vertical" />}>
            <span>总件数：{amount}</span>
            <span>总劳动时间：{renderHours(2)(workHours)}</span>
            <span>总休息时间：{renderHours(2)(restHours)}</span>
            <span>总带薪时间：{renderHours(2)(workHours - restHours)}</span>
            <span>总交通费：{formatterEn(travelFee)}</span>
            <span>总工作收入：{formatterEn(finalSalary)}</span>
          </Space>
        )}
        size="small"
        rowKey="_id"
        rowSelection={rowSelection}
        scroll={{ x: 2800, y: 'calc(100vh - 380px)' }}
        pagination={false}
      >
        <Table.Column
          fixed="left"
          title="申报状态"
          width={100}
          dataIndex="apply_status"
          render={(apply_status) => (
            <Tag color={salaryJSON.ApplyStatus?.[apply_status - 1]?.color}>
              {salaryJSON.ApplyStatus?.[apply_status - 1]?.label}
            </Tag>
          )}
        />
        <Table.Column
          title="日期"
          width={110}
          sorter
          render={(row) => (
            <InfoModel dataSource={row} refetch={salaryAPI.search.submit} />
          )}
        />
        <Table.Column
          title="教师姓名"
          width={100}
          sorter
          dataIndex="user_fullname_cn"
        />
        <Table.Column
          title="所属部⻔"
          width={160}
          sorter
          dataIndex="department_ids"
          render={([id1, id2]) => {
            const d1 = JSONfind(salaryJSON.Departments)(id1);
            const d2 = JSONfind(d1?.children)(id2);
            return d1?.label ? `${d1?.label}/${d2?.label}` : '';
          }}
        />
        <Table.Column width={160} title="工作内容" dataIndex="work_content" />
        <Table.Column
          title="计费方式"
          width={100}
          dataIndex="apply_type"
          render={renderLabel(salaryJSON.ApplyTypes)}
        />
        <Table.Column
          title="工作单价"
          width={100}
          dataIndex="salary_per"
          render={(v) => formatterEn(v)}
        />
        <Table.ColumnGroup title="工作时间">
          <Table.Column
            title="开始时间"
            width={100}
            dataIndex="start_time_str"
          />
          <Table.Column title="结束时间" width={100} dataIndex="end_time_str" />
        </Table.ColumnGroup>
        <Table.Column width={100} title="人/件/字数" dataIndex="amount" />
        <Table.ColumnGroup title="交通费">
          <Table.Column width={100} title="起始站" dataIndex="travel_start" />
          <Table.Column width={100} title="终点站" dataIndex="travel_end" />
          <Table.Column
            title="往返金额"
            width={100}
            dataIndex="travel_fee"
            render={(v) => formatterEn(v)}
          />
        </Table.ColumnGroup>
        <Table.Column
          width={100}
          title="劳动时间"
          dataIndex="work_hours"
          render={renderHours(2)}
        />
        <Table.Column
          width={100}
          title="休息时间"
          dataIndex="rest_hours"
          render={renderHours(2)}
        />
        <Table.Column title="备注" dataIndex="memo" />
        <Table.ColumnGroup title="负责人">
          <Table.Column
            title="姓名"
            fixed="right"
            width={80}
            dataIndex="check_user"
            render={(value) =>
              userOptions?.find((item) => item?.value === value)?.label
            }
          />
          <Table.Column
            title="审核状态"
            fixed="right"
            width={120}
            dataIndex="check_status"
            render={renderLabel(salaryJSON.CheckStatus)}
          />
        </Table.ColumnGroup>
        <Table.Column
          fixed="right"
          title="工作收入"
          width={160}
          dataIndex="final_salary"
          render={(v) => formatterEn(v)}
        />
        <Table.ColumnGroup fixed="right" title="转让审核操作">
          <Table.Column
            width={50}
            fixed="right"
            title="通过"
            render={(row) => {
              const handleChange = async () => {
                await updateSalaryRecord({
                  salaryRecordId: row?._id,
                  sub_check_status: 3,
                });
                salaryAPI.search.submit();
              };
              return (
                <Checkbox
                  checked={row?.sub_check_status === 3}
                  onClick={handleChange}
                />
              );
            }}
          />
          <Table.Column
            width={50}
            fixed="right"
            title="驳回"
            render={(row) => {
              const handleChange = async () => {
                await updateSalaryRecord({
                  salaryRecordId: row?._id,
                  sub_check_status: 4,
                });
                salaryAPI.search.submit();
              };
              return (
                <Checkbox
                  checked={row?.sub_check_status === 4}
                  onClick={handleChange}
                />
              );
            }}
          />
          <Table.Column
            width={50}
            fixed="right"
            title="待定"
            render={(row) => {
              const handleChange = async () => {
                await updateSalaryRecord({
                  salaryRecordId: row?._id,
                  sub_check_status: 2,
                });
                salaryAPI.search.submit();
              };
              return (
                <Checkbox
                  checked={row?.sub_check_status === 2}
                  onClick={handleChange}
                />
              );
            }}
          />
        </Table.ColumnGroup>
      </Table>
    </>
  );
};

export default ReviewWithTime;
