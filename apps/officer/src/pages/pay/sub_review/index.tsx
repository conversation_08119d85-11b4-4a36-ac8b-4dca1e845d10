import { useNavigate } from 'umi';
import { useRequest } from 'ahooks';
import { List } from 'antd';
////
import { dayjs } from '@/hooks/useDayjs';
import { useUserInfo } from '@/hooks/useUserInfo';
import { checkMonthStat } from '@/services/request/salaryRecords';

export interface PayReviewProps {}

const PayReview: React.FC<PayReviewProps> = () => {
  // state
  const navigate = useNavigate();
  const { userInfo } = useUserInfo();
  const api = useRequest(() =>
    checkMonthStat({ userId: userInfo?._id, isSub: true }),
  );

  const list: API.SalaryRecordStat[] = api?.data || [];

  return (
    <>
      <List
        header="转让审核列表"
        size="large"
        dataSource={list}
        renderItem={(item) => {
          const date = dayjs()
            ?.set('year', item?._id?.year)
            ?.set('month', item?._id?.month - 1);
          return (
            <List.Item
              actions={[
                <a
                  onClick={() =>
                    navigate(`/pay/sub_review/${date.format('YYYYMM')}`)
                  }
                >
                  审核
                </a>,
              ]}
            >
              <div className="flex justify-between align-middle w-full text-2xl">
                <span>{date.format('YYYY年MM月')}</span>
                <span>未审核 {item.undoneCount} 条</span>
                <span>共 {item.count} 条</span>
              </div>
            </List.Item>
          );
        }}
      />
    </>
  );
};

export default PayReview;
