import { useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
////
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.ZoomSetting {}

export interface ZoomFormProps extends SKFormProps<DataSource> {}

const ZoomForm: React.FC<ZoomFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue(props?.dataSource);
    }
  }, [props]);

  return (
    <Modal {...modalProps}>
      <Form
        name="ZoomForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
      >
        <Form.Item label="跟进类型" name="account">
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ZoomForm;
