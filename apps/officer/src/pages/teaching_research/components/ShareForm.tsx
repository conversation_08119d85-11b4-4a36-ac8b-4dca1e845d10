import { Modal, Form, Cascader, Button, Space, Typography } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
////
import { courseJSON } from '@/hooks/useJSON';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
};

interface DataSource extends API.CourseEvent {}

export interface ShareFormProps extends SKFormProps<DataSource> {
  options?: {};
}

const newCategory = courseJSON.Category?.map((item) => ({
  value: item.value,
  label: item.label,
  children: item.children?.map((item2) => ({
    value: item2.value,
    label: item2.label,
  })),
}));

const ShareForm: React.FC<ShareFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);

  const categories = Form.useWatch('categories', formProps.form);

  const encodeUrl = encodeURI(
    `${
      window.location.origin
    }/#/teaching_research/share?categories=${JSON.stringify(categories)}`,
  );

  return (
    <Modal {...modalProps} width={900} footer={null}>
      <Form name="TeachingResearchEditForm" labelAlign="left" {...formProps}>
        <Form.Item className="mt-8" label="分享地址">
          <Typography.Paragraph copyable style={{ margin: 0 }}>
            {`\u2002\u2002${encodeUrl}`}
          </Typography.Paragraph>
        </Form.Item>
        <Form.List name="categories">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Space
                  key={key}
                  style={{ display: 'flex', marginBottom: 8 }}
                  align="baseline"
                >
                  <Form.Item
                    key={key}
                    {...restField}
                    noStyle
                    name={[name]}
                    rules={[{ required: true, message: '课程属性是必填项' }]}
                  >
                    <Cascader style={{ width: 800 }} options={newCategory} />
                  </Form.Item>
                  <MinusCircleOutlined onClick={() => remove(name)} />
                </Space>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  添加课程属性
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default ShareForm;
