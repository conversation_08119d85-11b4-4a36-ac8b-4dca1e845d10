import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  message,
} from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
////
import { fixParams } from '@/utils/function';
import {
  createZoomSetting,
  deleteZoomSettingById,
  getAllZoomSettings,
  updateZoomSetting,
} from '@/services/request/zoomSettings';
import ZoomForm from './components/ZoomForm';
import { useForm } from '@/hooks/useSKForm';

export interface ZoomSettingsProps {}

const ZoomSettings: React.FC<ZoomSettingsProps> = () => {
  // state
  const [form] = Form.useForm();
  const { formType, formProps, handleOpen } = useForm<API.ZoomSetting>();

  // api
  const getTableData = async (_: any, formData: any) => {
    const data = await getAllZoomSettings({
      ...fixParams(false, formData),
    });
    return {
      total: data?.totalCount,
      list: (data?.zoomSettings as API.ZoomSetting[]) || [],
    };
  };
  const zoomAccountAPI = useAntdTable(getTableData, { form });

  // action
  const handleAdd = () => {
    handleOpen({
      title: '新建账号',
      type: 'add',
      data: null,
    });
  };

  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await createZoomSetting({
          ...v,
        });
      } else if (formType === 'edit') {
        if (formProps?.dataSource?._id) {
          await updateZoomSetting({
            ...v,
            zoomId: formProps?.dataSource?._id,
          });
        }
      }
      zoomAccountAPI.refresh();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  return (
    <>
      <ZoomForm type={formType} {...formProps} onSubmit={handleSubmit} />
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="150px">
            <Form.Item name="account_lk">
              <Input placeholder="账号名称" />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={zoomAccountAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={zoomAccountAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Button className="mb-4" type="primary" onClick={handleAdd}>
        新建
      </Button>
      <Table
        rowKey="_id"
        size="small"
        {...zoomAccountAPI.tableProps}
        pagination={{
          ...zoomAccountAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ y: 'calc( 100vh - 360px )' }}
      >
        <Table.Column sorter title="账号名称" dataIndex="account" />
        <Table.ColumnGroup title="操作">
          <Table.Column
            title="编辑"
            fixed="right"
            width={50}
            render={(row) => {
              const handleEdit = () => {
                handleOpen({
                  title: '编辑账号',
                  type: 'edit',
                  data: row,
                });
              };
              return (
                <Button
                  size="small"
                  type="primary"
                  ghost
                  disabled={row?.status === 1}
                  onClick={handleEdit}
                >
                  <EditOutlined />
                </Button>
              );
            }}
          />
          <Table.Column
            title="删除"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDelete = () => {
                Modal.confirm({
                  centered: true,
                  title: '删除',
                  content: '确认删除本ZOOM账号，删除后无法恢复。',
                  onOk: async () => {
                    await deleteZoomSettingById({
                      zoomId: row?._id,
                    });
                    zoomAccountAPI.search.submit();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button danger size="small" onClick={handleDelete}>
                  <DeleteOutlined />
                </Button>
              );
            }}
          />
        </Table.ColumnGroup>
      </Table>
    </>
  );
};

export default ZoomSettings;
