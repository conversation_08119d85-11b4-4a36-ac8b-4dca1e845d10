import { useState } from 'react';
import { useAntdTable } from 'ahooks';
import {
  Form,
  message,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  DatePicker,
  Select,
  Tag,
  Input,
  Cascader,
} from 'antd';
import { EditOutlined, DeleteOutlined, CopyOutlined } from '@ant-design/icons';
////
import { useForm } from '@/hooks/useSKForm';
import { courseJSON } from '@/hooks/useJSON';
import { dayjs, renderDate } from '@/hooks/useDayjs';
import { useUserOptions } from '@/hooks/useApiOptions';
import {
  getAllCourseEvents,
  createCourseEventAuto,
  createCourseEventBulk,
  deleteCourseEventById,
  updateCourseEvent,
  updateMultiCourseEvent,
} from '@/services/request/courseEvents';
import { filterOption, fixParams, fixUndefinedKey } from '@/utils/function';
import TeachingResearchForm from './components/TeachingResearchForm';

export interface TeacherListProps {}

const TeacherList: React.FC<TeacherListProps> = () => {
  // state
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>();
  const [form] = Form.useForm();
  const { formType, formProps, handleOpen } = useForm<API.CourseEvent>();
  const { userOptions, renderUserLabel } = useUserOptions();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllCourseEvents({
      ...fixParams(pageData, formData),
    });
    return {
      total: data?.totalCount,
      list: (data?.courseEvents as API.CourseEvent[]) || [],
    };
  };
  const courseEventsAPI = useAntdTable(getTableData, { form });

  // action
  const handleClear = () => {
    setSelectedRowKeys([]);
  };
  const handleAdd = () => {
    handleOpen({
      title: '开始排课',
      type: 'multi-create',
      data: null,
    });
  };
  const handleMultiUpdate = () => {
    handleOpen({
      title: '批量修改',
      type: 'multi-update',
      data: null,
    });
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'multi-create') {
        // Use the new bulk API - the data transformation is already handled in the form
        await createCourseEventBulk(v);
      }
      if (formType === 'edit') {
        if (!formProps.dataSource?._id) throw '目标课程错误';
        await updateCourseEvent({
          ...v,
          date: v?.date?.format('YYYY-MM-DD'),
          courseEventId: formProps.dataSource?._id,
        });
      }
      if (formType === 'multi-update') {
        await updateMultiCourseEvent({
          ...fixUndefinedKey(v),
          date: v?.date?.format('YYYY-MM-DD'),
          ids: selectedRowKeys,
        });
      }
      courseEventsAPI.refresh();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  // data
  const rowSelection: any = {
    type: 'check',
    fixed: true,
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange: (keys: any[]) => {
      setSelectedRowKeys(keys);
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.status !== 0,
    }),
  };
  const selected = selectedRowKeys?.length || 0;

  return (
    <>
      <TeachingResearchForm
        type={formType}
        {...formProps}
        onSubmit={handleSubmit}
      />
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="280px">
            <Form.Item name="date_range">
              <DatePicker.RangePicker
                placeholder={['开课日期', '开课日期']}
                allowEmpty={[true, true]}
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="status">
              <Select
                showSearch
                placeholder="课程状态"
                options={courseJSON.Status}
                filterOption={filterOption}
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="teacher">
              <Select
                showSearch
                placeholder="教师姓名"
                options={userOptions}
                filterOption={filterOption}
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="name_lk">
              <Input placeholder="课程名称" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="category">
              <Cascader
                showSearch
                placeholder="课程属性"
                options={courseJSON.Category}
              />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={courseEventsAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={courseEventsAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Row className='mb-2'>
        <Col>
          <Space>
            <span>已选: {selected} 项</span>
            <Button size="small" type="link" onClick={handleClear}>
              清除已选
            </Button>
          </Space>
        </Col>
        <Col flex="auto"></Col>
        <Col>
          <Space>
            <span>批量操作：</span>
            <Button
              type="primary"
              disabled={!selectedRowKeys}
              onClick={handleMultiUpdate}
            >
              編集
            </Button>
            <Button type="primary" onClick={handleAdd}>
              新建排课
            </Button>
          </Space>
        </Col>
      </Row>

      <Table
        rowKey="_id"
        size="small"
        rowSelection={rowSelection}
        {...courseEventsAPI.tableProps}
        pagination={{
          ...courseEventsAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ y: 'calc( 100vh - 320px )' }}
      >
        <Table.Column
          sorter
          width={80}
          title="课程状态"
          dataIndex="status"
          render={(s) => {
            const status = courseJSON.Status?.[s];
            return <Tag color={status?.color}>{status?.label}</Tag>;
          }}
        />
        <Table.Column sorter width={150} title="课程名称" dataIndex="name" />
        <Table.Column
          width={200}
          sorter
          title="课程属性"
          dataIndex="category"
          render={(c: string[][]) => c?.map((cat: string[]) => cat.join(' - ')).join(', ')}
        />
        <Table.Column<API.User>
          width={80}
          sorter
          title="教师姓名"
          dataIndex="teacher"
          render={renderUserLabel}
        />
        <Table.Column
          sorter
          width={150}
          title="zoom账号"
          dataIndex="zoom_account"
        />
        <Table.ColumnGroup title="开课时日">
          <Table.Column
            sorter
            title="开课日期"
            width={80}
            dataIndex="date"
            render={renderDate('YYYY.MM.DD')}
          />
          <Table.Column
            title="开始时间"
            width={60}
            dataIndex="start_date_str"
          />
          <Table.Column title="结束时间" width={60} dataIndex="end_date_str" />
        </Table.ColumnGroup>
        <Table.ColumnGroup title="操作">
          <Table.Column
            title="编辑"
            fixed="right"
            width={50}
            render={(row) => {
              const handleEdit = () => {
                handleOpen({
                  title: '编辑排课信息',
                  type: 'edit',
                  data: row,
                });
              };
              return (
                <Button
                  disabled={!!row?.status}
                  size="small"
                  onClick={handleEdit}
                >
                  <EditOutlined />
                </Button>
              );
            }}
          />
          <Table.Column
            title="复制"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDuplicate = () => {
                // Create a copy of the row data excluding the date field and _id
                const duplicateData = {
                  ...row,
                  date: undefined, // Clear the course start date
                  _id: undefined, // Clear the ID so it creates a new record
                };
                handleOpen({
                  title: '复制排课',
                  type: 'multi-create',
                  data: duplicateData,
                });
              };
              return (
                <Button
                  size="small"
                  onClick={handleDuplicate}
                >
                  <CopyOutlined />
                </Button>
              );
            }}
          />
          <Table.Column
            title="删除"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDelete = () => {
                Modal.confirm({
                  centered: true,
                  title: '删除',
                  content: '确认删除本课程，删除后无法恢复。',
                  onOk: async () => {
                    await deleteCourseEventById({
                      courseEventId: row?._id,
                    });
                    courseEventsAPI.refresh();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button
                  disabled={!!row?.status}
                  size="small"
                  onClick={handleDelete}
                >
                  <DeleteOutlined />
                </Button>
              );
            }}
          />
        </Table.ColumnGroup>
      </Table>
    </>
  );
};

export default TeacherList;
