import { createSearchParams, useNavigate } from 'umi';
import { useRequest } from 'ahooks';
import { useState } from 'react';
import {
  Button,
  Calendar,
  Card,
  Col,
  List,
  Modal,
  Row,
  Select,
  Space,
  Tag,
  message,
} from 'antd';
////
import { dayjs } from '@/hooks/useDayjs';
import { useForm } from '@/hooks/useSKForm';
import { courseJSON } from '@/hooks/useJSON';
import { useUserOptions } from '@/hooks/useApiOptions';
import {
  createCourseEventAuto,
  createCourseEventBulk,
  finishCourseEventById,
  getAllCourseEvents,
  updateCourseEvent,
  toggleCourseEventNotification,
} from '@/services/request/courseEvents';
import TeachingResearchForm from './components/TeachingResearchForm';
import ShareForm from './components/ShareForm';

const autoCourses = [
  '正课',
  '习题课',
  '过去问课',
  'TA课',
  '习题课',
  '日语',
  '托福',
  '托业',
];

export interface TeachingResearchProps {}

const TeachingResearch: React.FC<TeachingResearchProps> = () => {
  // state
  const { formType, formProps, handleOpen } = useForm<API.CourseEvent>();
  const shareForm = useForm<API.CourseEvent>();
  const navigate = useNavigate();
  const [date, setDate] = useState(dayjs());
  const { renderUserLabel } = useUserOptions();

  // api
  const monthApi = useRequest(
    async () =>
      await getAllCourseEvents({
        sortField: 'date',
        sortOrder: 1,
        perPage: 1000, // Set high limit to fetch all events in 3-month range
        // has_notification: true,
        date_start: date.startOf('M').add(-1, 'M').format('YYYY-MM-DD'),
        date_end: date.endOf('M').add(1, 'M').format('YYYY-MM-DD'),
      }),
    { refreshDeps: [date] },
  );

  const dateApi = useRequest(
    async () =>
      await getAllCourseEvents({
        sortField: 'start_date_str',
        sortOrder: 1,
        date_start: date.startOf('D').format('YYYY-MM-DD'),
        date_end: date.endOf('D').format('YYYY-MM-DD'),
      }),
    { refreshDeps: [date] },
  );

  const handleAdd = () => {
    handleOpen({
      title: '开始排课',
      type: 'multi-create',
      data: null,
    });
  };

  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'multi-create') {
        // Use the new bulk API - the data transformation is already handled in the form
        await createCourseEventBulk(v);
      }
      if (formType === 'edit') {
        if (!formProps.dataSource?._id) throw '目标课程错误';
        await updateCourseEvent({
          ...v,
          date: v?.date?.format('YYYY-MM-DD'),
          courseEventId: formProps.dataSource?._id,
        });
      }
      monthApi.refresh();
      dateApi.refresh();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  const handleOpenShareForm = () => {
    shareForm.handleOpen({
      title: '共享课表',
      type: 'share',
      data: null,
    });
  };

  const handleSubmitShareForm = async (v: any) => {
    // try {
    //   await navigator.clipboard.writeText(
    //     `${window.location.origin}/#/teaching_research/share?categories=${JSON.stringify(v?.categories)}`,
    //   );
    //   message.success("共享地址复制成功")
    // } catch (e) {
    //   console.log(e)
    //   message.error("共享地址复制失败")
    // }
  };

  return (
    <Row gutter={24}>
      <Col>
        <Card title={`今日课程 ${dateApi?.data?.courseEvents?.length} 门`}>
          <Calendar
            style={{ width: 350 }}
            fullscreen={false}
            value={date}
            onSelect={setDate}
            onPanelChange={monthApi.runAsync}
            headerRender={({ value, onChange }) => {
              const start = 0;
              const end = 12;
              const monthOptions = [];

              let current = value.clone();
              const localeData = value.localeData();
              const months = [];
              for (let i = 0; i < 12; i++) {
                current = current.month(i);
                months.push(localeData.monthsShort(current));
              }

              for (let i = start; i < end; i++) {
                monthOptions.push(
                  <Select.Option key={i} value={i} className="month-item">
                    {months[i]}
                  </Select.Option>,
                );
              }

              const year = value.year();
              const month = value.month();
              const options = [];
              for (let i = year - 10; i < year + 10; i += 1) {
                options.push(
                  <Select.Option key={i} value={i} className="year-item">
                    {i}
                  </Select.Option>,
                );
              }

              return (
                <Row gutter={8} justify="end" className="mb-2">
                  <Col>
                    <Select
                      size="small"
                      dropdownMatchSelectWidth={false}
                      value={year}
                      onChange={(newYear) => {
                        const now = value.clone().year(newYear);
                        onChange(now);
                      }}
                    >
                      {options}
                    </Select>
                  </Col>
                  <Col>
                    <Select
                      size="small"
                      dropdownMatchSelectWidth={false}
                      value={month}
                      onChange={(newMonth) => {
                        const now = value.clone().month(newMonth);
                        onChange(now);
                      }}
                    >
                      {monthOptions}
                    </Select>
                  </Col>
                </Row>
              );
            }}
            dateCellRender={(date) => {
              // First, get all events with notifications in the month range
              const eventsWithNotifications = monthApi?.data?.courseEvents?.filter((item: any) =>
                item?.has_notification === true
              ) || [];

              // Then check if current date has any events with notifications
              const dateStr = date.format('YYYY-MM-DD');
              const hasNotification = eventsWithNotifications.some((item: any) => {
                const itemDateStr = dayjs(item?.date).format('YYYY-MM-DD');
                return itemDateStr === dateStr;
              });

              const style = {
                fontSize: 25,
                color: hasNotification ? '#ff4d4f' : 'white',
              };
              return <span style={style}>・</span>;
            }}
          />
        </Card>
      </Col>
      <Col flex="auto">
        <Card
          title={date?.format(`YYYY年MM月DD日 课程概览`)}
          extra={
            <Space>
              <Button size="small" onClick={handleAdd}>
                排课
              </Button>
              <Button size="small" onClick={handleOpenShareForm}>
                共享课表
              </Button>
            </Space>
          }
        >
          <TeachingResearchForm
            type={formType}
            {...formProps}
            onSubmit={handleSubmit}
          />
          <ShareForm
            type={shareForm.formType}
            {...shareForm.formProps}
            onSubmit={handleSubmitShareForm}
          />
          <List<API.CourseEvent>
            size="small"
            itemLayout="vertical"
            dataSource={dateApi?.data?.courseEvents}
            renderItem={(item) => {
              const status = courseJSON.Status?.[item?.status];
              // Check if any category path has an auto course type
              const isAuto = item?.category?.some((categoryPath: string[]) =>
                autoCourses.includes(categoryPath?.[2])
              ) || false;
              return (
                <List.Item
                  extra={
                    <table style={{
                      width: '280px',
                      borderCollapse: 'collapse',
                      fontSize: '14px',
                      lineHeight: '1.6',
                      tableLayout: 'fixed'
                    }}>
                      <tbody>
                        <tr>
                          <td style={{
                            verticalAlign: 'top',
                            fontWeight: '500',
                            width: '80px',
                            paddingRight: '8px',
                            paddingBottom: '4px',
                            textAlign: 'left'
                          }}>
                            课程属性：
                          </td>
                          <td style={{
                            verticalAlign: 'top',
                            paddingBottom: '4px',
                            textAlign: 'left'
                          }}>
                            <div style={{ textAlign: 'left' }}>{item?.category?.[0]?.join('-') || ''}</div>
                            {item?.category?.slice(1).map((cat: string[], index: number) => (
                              <div key={index + 1} style={{ textAlign: 'left' }}>{cat.join('-')}</div>
                            ))}
                          </td>
                        </tr>
                        <tr>
                          <td style={{
                            verticalAlign: 'top',
                            fontWeight: '500',
                            width: '80px',
                            paddingRight: '8px',
                            paddingBottom: '4px',
                            textAlign: 'left'
                          }}>
                            上课老师：
                          </td>
                          <td style={{
                            verticalAlign: 'top',
                            paddingBottom: '4px',
                            textAlign: 'left'
                          }}>
                            {renderUserLabel(item?.teacher)}
                          </td>
                        </tr>
                        <tr>
                          <td style={{
                            verticalAlign: 'top',
                            fontWeight: '500',
                            width: '80px',
                            paddingRight: '8px',
                            paddingBottom: '4px',
                            textAlign: 'left'
                          }}>
                            使用账号：
                          </td>
                          <td style={{
                            verticalAlign: 'top',
                            paddingBottom: '4px',
                            textAlign: 'left'
                          }}>
                            {item?.zoom_account || '暂缺'}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  }
                  actions={[
                    <Button
                      key="list-edit"
                      size="small"
                      type="primary"
                      ghost
                      disabled={!!item?.status}
                      onClick={() =>
                        handleOpen({
                          title: '编辑排课信息',
                          type: 'edit',
                          data: item,
                        })
                      }
                    >
                      编辑
                    </Button>,
                    isAuto ? (
                      <Button
                        key="list-more"
                        size="small"
                        type="primary"
                        disabled={!!item?.status}
                        onClick={() => {
                          Modal.confirm({
                            centered: true,
                            title: '完成并申报',
                            content:
                              '确认本教师已完成本课程，并已设置基本工资。',
                            onOk: async () => {
                              const res = await finishCourseEventById({
                                courseEventId: item?._id,
                              });
                              message.info(res.message);
                              // Auto-clear notification when course is completed
                              if (item?.has_notification) {
                                await updateCourseEvent({
                                  courseEventId: item?._id,
                                  has_notification: false,
                                });
                              }
                              monthApi.refresh();
                              dateApi.refresh();
                            },
                            okText: '确认',
                            cancelText: '取消',
                          });
                        }}
                      >
                        完成
                      </Button>
                    ) : (
                      <Button
                        key="list-more"
                        size="small"
                        type="link"
                        disabled={!!item?.status}
                        onClick={() => {
                          Modal.confirm({
                            centered: true,
                            title: '完成',
                            content: '确认本教师已完成本课程。',
                            onOk: async () => {
                              await updateCourseEvent({
                                courseEventId: item?._id,
                                status: 1,
                                has_notification: false, // Auto-clear notification when course is completed
                              });
                              monthApi.refresh();
                              dateApi.refresh();
                            },
                            okText: '确认',
                            cancelText: '取消',
                          });
                        }}
                      >
                        完成
                      </Button>
                    ),
                    <Button
                      key="list-more"
                      size="small"
                      type="link"
                      danger
                      disabled={!!item?.status}
                      onClick={() => {
                        Modal.confirm({
                          centered: true,
                          title: '缺席',
                          content:
                            '确认本教师临时缺席课程，需要另行安排补课时间。',
                          onOk: async () => {
                            await updateCourseEvent({
                              courseEventId: item?._id,
                              status: 2,
                            });
                            dateApi.refresh();
                          },
                          okText: '确认',
                          cancelText: '取消',
                        });
                      }}
                    >
                      缺席
                    </Button>,
                    <Button
                      key="list-notification"
                      size="small"
                      type={item?.has_notification ? "primary" : "default"}
                      disabled={!!item?.status}
                      onClick={async () => {
                        try {
                          const res = await toggleCourseEventNotification({
                            courseEventId: item?._id,
                          });
                          message.success(res?.message || '提醒状态已更新');
                          monthApi.refresh();
                          dateApi.refresh();
                        } catch (error: any) {
                          message.error(error?.message || '操作失败');
                        }
                      }}
                    >
                      提醒
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Tag color={status?.color}>{status?.label}</Tag>}
                    title={
                      <div>
                        {`${item?.name} 【${renderUserLabel(item?.teacher)}】`}
                      </div>
                    }
                    description={
                      <span>
                        {item?.start_date_str} ~ {item?.end_date_str}
                      </span>
                    }
                  />
                </List.Item>
              );
            }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default TeachingResearch;
