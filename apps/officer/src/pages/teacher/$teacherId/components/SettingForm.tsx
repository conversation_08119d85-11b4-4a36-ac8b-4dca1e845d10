import {
  Row,
  Col,
  Form,
  Input,
  Select,
  Button,
  InputNumber,
  Cascader,
} from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
////
import { userJSON, courseJSON } from '@/hooks/useJSON';
import { Access, useAccess } from '@/hooks/useAccess';

export interface SettingFormProps {}

const SettingForm: React.FC<SettingFormProps> = () => {
  const access = useAccess();
  const rolesOptions = [
    { "value": "咨询", "label": "咨询", "color": "" },
    { "value": "咨询管理员", "label": "咨询管理员", "color": "" },
    { "value": "销售", "label": "销售", "color": "" },
    { "value": "销售管理员", "label": "销售管理员", "color": "" },
    { "value": "教务", "label": "教务", "color": "" },
    { "value": "教务管理员", "label": "教务管理员", "color": "" },
    { "value": "学部班主任", "label": "学部班主任", "color": "" },
    { "value": "学部班主任管理员", "label": "学部班主任管理员", "color": "" },
    { "value": "大学院班主任", "label": "大学院班主任", "color": "" },
    { "value": "大学院班主任管理员", "label": "大学院班主任管理员", "color": "" }
  ];
  return (
    <Row>
      <Col flex="720px">
        <Form.Item label="可带课程" name="can_courses">
          <Input />
        </Form.Item>
        <Form.Item label="（预）毕业院校名" name="graduate_university">
          <Input />
        </Form.Item>
        <Form.Item label="专业名" name="faculty">
          <Input />
        </Form.Item>
        <Form.Item label="（预）毕业时间" name="graduate_date">
          <Input />
        </Form.Item>
        <Form.Item label="（预）取得学位" name="degree">
          <Input />
        </Form.Item>
        <Access accessible={access.isSuper}>
          <Form.Item label="系统权限" name="role">
            <Select options={userJSON.Role} />
          </Form.Item>
        </Access>
        <Access accessible={access.page?.['teacher.setting.UPDATE_ROLES']}>
          <Form.Item label="身份权限" name="roles">
            <Select mode="multiple" options={rolesOptions} />
          </Form.Item>
        </Access>
        <Access accessible={access.page?.['teacher.setting.UPDATE_ON_STATUS']}>
          <Form.Item label="在职状态" name="status">
            <Select options={userJSON.Status} />
          </Form.Item>
        </Access>
        <Access
          accessible={access.page?.['teacher.setting.UPDATE_BASE_SALARY']}
        >
          <Form.Item label="VIP工资" name="vip_salary">
            <InputNumber
              min={0}
              step={1}
              style={{ width: "100%" }}
              placeholder="VIP工资（日元）"
            />
          </Form.Item>
          <Form.Item label="基础工资">
            <Form.List name="base_salary">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <div key={key} className="flex space-x-2">
                      <Form.Item
                        {...restField}
                        name={[name, 'name']}
                        rules={[
                          { required: true, message: '工资种类不能为空' },
                        ]}
                      >
                        <Cascader
                          style={{ width: 300 }}
                          options={courseJSON.Category}
                          placeholder="工资种类"
                        />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'salary']}
                        rules={[
                          { required: true, message: '工资（日元）不能为空' },
                        ]}
                      >
                        <InputNumber
                          min={0}
                          step={1}
                          style={{ width: 120 }}
                          placeholder="工资（日元）"
                        />
                      </Form.Item>
                      <Form.Item>
                        <MinusCircleOutlined onClick={() => remove(name)} />
                      </Form.Item>
                    </div>
                  ))}
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusOutlined />}
                    >
                      添加
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>
        </Access>
      </Col>
    </Row>
  );
};

export default SettingForm;
