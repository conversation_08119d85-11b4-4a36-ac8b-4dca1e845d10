import { Row, Col, Form, Input, Select, Space } from 'antd';

export interface BasicFormProps {}

const BasicForm: React.FC<BasicFormProps> = () => {
  return (
    <Row>
      <Col flex="600px">
        <Form.Item label="姓名（汉字）" required>
          <Space>
            <Form.Item name="last_name_cn" rules={[{ required: true }]} noStyle>
              <Input placeholder="姓（汉字）" />
            </Form.Item>
            <Form.Item
              name="first_name_cn"
              rules={[{ required: true }]}
              noStyle
            >
              <Input placeholder="名（汉字）" />
            </Form.Item>
          </Space>
        </Form.Item>

        <Form.Item label="姓名（拼⾳）" required>
          <Space>
            <Form.Item name="last_name_en" rules={[{ required: true }]} noStyle>
              <Input placeholder="姓（拼⾳）" />
            </Form.Item>
            <Form.Item
              name="first_name_en"
              rules={[{ required: true }]}
              noStyle
            >
              <Input placeholder="名（拼⾳）" />
            </Form.Item>
          </Space>
        </Form.Item>
        <Form.Item label="姓名（カタカナ）" required>
          <Space>
            <Form.Item name="last_name_jp" noStyle>
              <Input placeholder="姓（カタカナ）" />
            </Form.Item>
            <Form.Item name="first_name_jp" noStyle>
              <Input placeholder="名（カタカナ）" />
            </Form.Item>
          </Space>
        </Form.Item>
        <Form.Item
          label="⽣年⽉⽇"
          name="birthday"
          rules={[{ required: true }]}
        >
          <Input placeholder="例：19920101" />
        </Form.Item>
        <Form.Item label="性别" name="gender" rules={[{ required: true }]}>
          <Select
            style={{ width: 80 }}
            options={[
              { value: 2, label: '女' },
              { value: 1, label: '男' },
            ]}
          />
        </Form.Item>
      </Col>
    </Row>
  );
};

export default BasicForm;
