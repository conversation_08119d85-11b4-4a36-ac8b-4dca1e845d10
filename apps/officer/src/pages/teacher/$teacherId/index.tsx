import { use<PERSON>arams } from 'umi';
import { useState } from 'react';
import { useRequest } from 'ahooks';
import { Button, Card, Form, message } from 'antd';
import dayjs from 'dayjs';
////
import PayForm from './components/PayForm';
import BasicForm from './components/BasicForm';
import DocumentsForm from './components/DocumentsForm';
import { getUserById, updateUser } from '@/services/request/user';
import { fixFileListToUrl, fixUrlToFileList } from './components/S3Upload';
import ResetPasswordForm from './components/ResetPasswordForm';
import SettingForm from './components/SettingForm';
import { useAccess } from '@/hooks/useAccess';

export interface ProfileSettingProps {}

const superTabList = [
  {
    key: 'setting',
    tab: '教务相关',
  },
  {
    key: 'basic',
    tab: '基础信息',
  },
  {
    key: 'documents',
    tab: '证件信息',
  },
  {
    key: 'pay',
    tab: '账户信息',
  },
  {
    key: 'reset',
    tab: '重设密码',
  },
];
const normalTabList = [
  {
    key: 'setting',
    tab: '教务相关',
  },
];

const contentList: Record<string, React.ReactNode> = {
  setting: <SettingForm />,
  pay: <PayForm />,
  basic: <BasicForm />,
  documents: <DocumentsForm />,
  reset: <ResetPasswordForm />,
};

const ProfileSetting: React.FC<ProfileSettingProps> = () => {
  // state
  const access = useAccess();
  const [activeTabKey, setActiveTabKey] = useState<string>('setting');
  const [form] = Form.useForm();
  const { teacherId = '' } = useParams();

  const tabList = access.isSuper ? superTabList : normalTabList;

  // api
  const userAPI = useRequest(
    async () => await getUserById({ userId: teacherId }),
    {
      onSuccess: (data: API.User) => {
        form.setFieldsValue({
          ...data,
          ID_expiry_date: data?.ID_expiry_date
            ? dayjs(data?.ID_expiry_date)
            : null,
          ID_images: fixUrlToFileList(data?.ID_images),
          bank_account: data?.bank_account
            ? {
                ...data?.bank_account,
                bank_account_images: fixUrlToFileList(
                  data?.bank_account?.bank_account_images,
                ),
                CN_ID_images: fixUrlToFileList(
                  data?.bank_account?.CN_ID_images,
                ),
              }
            : null,
        });
      },
    },
  );
  const updateUserAPI = useRequest(updateUser, {
    manual: true,
  });

  // action
  const onTabChange = (key: string) => {
    setActiveTabKey(key);
  };
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (activeTabKey === 'reset') {
        const res = await updateUserAPI.runAsync({
          userId: teacherId,
          password: '123456',
        });
        message.warning(res?.message);
      } else if (activeTabKey === 'pay') {
        const nesUser = {
          userId: teacherId,
          ...values,
          bank_account: values?.bank_account
            ? {
                ...values?.bank_account,
                bank_account_images: fixFileListToUrl(
                  values?.bank_account?.bank_account_images,
                ),
                CN_ID_images: fixFileListToUrl(
                  values?.bank_account?.CN_ID_images,
                ),
              }
            : null,
        };
        const res = await updateUserAPI.runAsync(nesUser);
        message.warning(res?.message);
      } else if (activeTabKey === 'documents') {
        const res = await updateUserAPI.runAsync({
          userId: teacherId,
          ...values,
          ID_images: fixFileListToUrl(values?.ID_images),
        });
        message.warning(res?.message);
      } else if (activeTabKey === 'basic') {
        const res = await updateUserAPI.runAsync({
          userId: teacherId,
          ...values,
        });
        message.warning(res?.message);
      } else if (activeTabKey === 'setting') {
        const res = await updateUserAPI.runAsync({
          userId: teacherId,
          ...values,
        });
        message.warning(res?.message);
      }
    } catch (err: any) {
      message.error(err?.message);
      console.log('Field:', err);
    }
  };

  return (
    <Card
      className="grid"
      title={`${userAPI.data?.last_name_cn || ''} ${
        userAPI.data?.first_name_cn || ''
      }`}
      style={{
        gridTemplateRows: '98px 1fr 64px',
        height: '100%',
      }}
      bodyStyle={{
        height: '100%',
        overflow: 'scroll',
      }}
      tabList={tabList}
      activeTabKey={activeTabKey}
      onTabChange={onTabChange}
      actions={[
        <Button type="primary" onClick={handleSubmit}>
          提交
        </Button>,
      ]}
    >
      <Form
        name="UserSettingForm"
        form={form}
        labelCol={{ span: 12 }}
        wrapperCol={{ span: 12 }}
        onValuesChange={(values) => {
          if (values.ID_type) {
            form.validateFields(['ID_images']); // 校验 ID_images
          }
        }}
        validateMessages={{ required: "'${label}' 是必填项" }}
        autoComplete="off"
      >
        {contentList[activeTabKey]}
      </Form>
    </Card>
  );
};

export default ProfileSetting;
