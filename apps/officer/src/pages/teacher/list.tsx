import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  Tag,
  Select,
} from 'antd';
import { RollbackOutlined, UserDeleteOutlined } from '@ant-design/icons';
////
import { fixParams } from '@/utils/function';
import { getAllUsers, updateUser } from '@/services/request/user';
import { JSONfind, userJSON } from '@/hooks/useJSON';
import { renderAccess, useAccess } from '@/hooks/useAccess';

export interface TeacherListProps {}

const TeacherList: React.FC<TeacherListProps> = () => {
  // state
  const access = useAccess();
  const [form] = Form.useForm();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllUsers({
      status_in: JSON.stringify([1, 2]),
      ...fixParams(pageData, formData),
    });
    return {
      total: data?.totalCount,
      list: (data?.users as API.Student[]) || [],
    };
  };
  const teachersAPI = useAntdTable(getTableData, { form });

  // render
  const renderTeacherResign = renderAccess({
    accessible: access.page?.['teacher.list.RESIGN'],
    children: (
      <Table.Column
        title="离职"
        fixed="right"
        width={50}
        render={(row) => {
          const handleDelete = () => {
            Modal.confirm({
              centered: true,
              title: '离职',
              content:
                '确认本教师已离职，离职后无法使用本系统，但可以使用工资申报系统。',
              onOk: async () => {
                await updateUser({
                  userId: row?._id,
                  status: 2,
                });
                teachersAPI.search.submit();
              },
              okText: '确认',
              cancelText: '取消',
            });
          };
          return (
            <Button
              danger
              size="small"
              disabled={row?.status === 2}
              onClick={handleDelete}
            >
              <UserDeleteOutlined />
            </Button>
          );
        }}
      />
    ),
  });

  return (
    <>
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="150px">
            <Form.Item name="status_in">
              <Select options={userJSON.Status} placeholder="在职状态" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="full_name_cn_like">
              <Input placeholder="教师姓名" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="can_courses_lk">
              <Input placeholder="可带课程" />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={teachersAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={teachersAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowKey="_id"
        size="small"
        {...teachersAPI.tableProps}
        pagination={{
          ...teachersAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ y: 'calc( 100vh - 320px )' }}
      >
        <Table.Column<API.User>
          sorter
          fixed="left"
          width={150}
          title="教师姓名"
          dataIndex="full_name_cn"
          render={(_, row) => (
            <Link to={`/teacher/${row?._id}`}>{row?.full_name_cn || '-'}</Link>
          )}
        />
        <Table.Column
          sorter
          width={220}
          title="系统权限"
          dataIndex="roles"
          render={(roles) => roles?.join('; ')}
        />
        <Table.Column
          sorter
          width={120}
          title="毕业院校"
          dataIndex="graduate_university"
        />
        <Table.Column width={120} title="专业专攻" dataIndex="faculty" />
        <Table.Column width={250} title="可带课程" dataIndex="can_courses" />
        <Table.Column
          title="在职状态"
          width={80}
          dataIndex="status"
          render={(status) => {
            const temp = JSONfind(userJSON.Status)(status);
            return <Tag color={temp?.color}>{temp?.label}</Tag>;
          }}
        />
        <Table.ColumnGroup title="操作">
          <Table.Column
            title="复职"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDelete = () => {
                Modal.confirm({
                  centered: true,
                  title: '复职',
                  content: '确认本教师已复职，复职后可以使用本系统。',
                  onOk: async () => {
                    await updateUser({
                      userId: row?._id,
                      status: 1,
                    });
                    teachersAPI.search.submit();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button
                  size="small"
                  type="primary"
                  ghost
                  disabled={row?.status === 1}
                  onClick={handleDelete}
                >
                  <RollbackOutlined />
                </Button>
              );
            }}
          />
          {renderTeacherResign}
        </Table.ColumnGroup>
      </Table>
    </>
  );
};

export default TeacherList;
