import { Link } from 'umi';
import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  DatePicker,
} from 'antd';
import { SendOutlined } from '@ant-design/icons';
////
import { fixParams } from '@/utils/function';
import { renderDate } from '@/hooks/useDayjs';
import { getAllUsers, updateUser } from '@/services/request/user';

export interface TeacherListProps {}

const TeacherList: React.FC<TeacherListProps> = () => {
  // state
  const [form] = Form.useForm();

  // api
  const getTableData = async (pageData: any, formData: any) => {
    const data = await getAllUsers({
      ...fixParams(pageData, formData),
      status: 0,
    });
    return {
      total: data?.totalCount,
      list: (data?.users as API.Student[]) || [],
    };
  };
  const teachersAPI = useAntdTable(getTableData, { form });

  return (
    <>
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="280px">
            <Form.Item name="createdAt_range">
              <DatePicker.RangePicker
                placeholder={['创建时间', '创建时间']}
                allowEmpty={[true, true]}
              />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="full_name_cn_like">
              <Input placeholder="教师姓名" />
            </Form.Item>
          </Col>
          <Col flex="150px">
            <Form.Item name="graduate_university_lk">
              <Input placeholder="毕业院校" />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={teachersAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={teachersAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowKey="_id"
        size="small"
        {...teachersAPI.tableProps}
        pagination={{
          ...teachersAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ y: 'calc( 100vh - 320px )' }}
      >
        <Table.Column
          sorter
          fixed="left"
          title="创建时间"
          width={150}
          dataIndex="createdAt"
          render={renderDate('YYYY.MM.DD HH:mm')}
        />
        <Table.Column<API.User>
          sorter
          fixed="left"
          width={150}
          title="教师姓名"
          dataIndex="full_name_cn"
          render={(_, row) => (
            <Link to={`/teacher/${row?._id}`}>{row?.full_name_cn || '-'}</Link>
          )}
        />
        <Table.Column
          sorter
          width={120}
          title="毕业院校"
          dataIndex="graduate_university"
        />
        <Table.ColumnGroup title="操作">
          <Table.Column
            title="入职"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDelete = () => {
                Modal.confirm({
                  centered: true,
                  title: '入职',
                  content: '确认本教师已分配本系统相关权限。',
                  onOk: async () => {
                    await updateUser({
                      userId: row?._id,
                      status: 1,
                    });
                    teachersAPI.search.submit();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button
                  size="small"
                  disabled={row?.status === 1}
                  onClick={handleDelete}
                >
                  <SendOutlined />
                </Button>
              );
            }}
          />
        </Table.ColumnGroup>
      </Table>
    </>
  );
};

export default TeacherList;
