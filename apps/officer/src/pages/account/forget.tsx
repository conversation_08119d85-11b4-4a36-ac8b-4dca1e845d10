import { useNavigate } from 'umi';
import { Button, Card, Form, Input } from 'antd';

export interface forgetProps {}

const forget: React.FC<forgetProps> = () => {
  // state
  const navigate = useNavigate();

  // action
  const onBack = () => {
    navigate('/');
  };

  const onFinish = (values: any) => {
    console.log('Success:', values);
    navigate('/');
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className="flex h-screen">
      <Card
        className="m-auto w-96"
        title="忘记密码 - 新领域理⼯塾"
        extra={<Button onClick={onBack}>返回</Button>}
      >
        <Form
          name="basic"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={{ remember: true }}
          onFinish={onFinish}
          labelAlign="left"
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            label="邮箱"
            name="email"
            rules={[{ required: true, message: '请输入邮箱!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Button type="primary" htmlType="submit">
              发送邮件
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default forget;
