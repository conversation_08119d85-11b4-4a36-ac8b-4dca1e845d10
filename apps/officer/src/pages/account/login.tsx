import { useNavigate } from 'umi';
import { useRequest } from 'ahooks';
import { Button, Card, Form, Input, message } from 'antd';
////
import { userLogin } from '@/services/request/user';
import { useUserInfo } from '@/hooks/useUserInfo';

export interface loginProps {}

const login: React.FC<loginProps> = () => {
  // state
  const navigate = useNavigate();
  const { updateUserInfo } = useUserInfo();
  const userLoginApi = useRequest(userLogin, {
    manual: true,
  });

  // action

  const onFinish = async (values: any) => {
    try {
      const res = await userLoginApi.runAsync(values);
      if (!res?._id) throw res;
      updateUserInfo(res);
      navigate('/pay/review');
    } catch (error: any) {
      message.error(error?.message || 'error');
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className="flex h-screen">
      <Card className="m-auto w-96" title="登陆 - 新领域理⼯塾">
        <Form
          name="basic"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={{ remember: true }}
          onFinish={onFinish}
          labelAlign="left"
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            label="邮箱"
            name="email"
            rules={[{ required: true, message: '请输入邮箱!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Button type="primary" htmlType="submit">
              登陆
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default login;
