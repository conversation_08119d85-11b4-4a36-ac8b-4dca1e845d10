// 风格化日元
export function formatterEn(value: any, def?: string) {
  if (value) {
    return `￥ ${value}`?.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  } else {
    return def || '-';
  }
}

// 统一修正Table翻页及排序参数
export const fixPageData = (pageData: any) => {
  let sorter: any = {
    sortField: 'createdAt',
    sortOrder: -1,
  };
  if (Array.isArray(pageData?.sorter?.field)) {
    sorter.sortField = pageData?.sorter?.field?.join('.');
  } else if (!!pageData?.sorter?.field) {
    sorter.sortField = pageData?.sorter?.field;
  }
  if (pageData?.sorter?.order === 'ascend') {
    sorter.sortOrder = 1;
  }
  if (pageData?.sorter?.order === 'descend') {
    sorter.sortOrder = -1;
  }
  return {
    page: pageData.current - 1,
    perPage: pageData.pageSize,
    ...sorter,
  };
};
// 统一修正Form搜索参数
export const fixFormData = (formData: any) => {
  let res: any = {};
  for (const [key, value] of Object.entries<any>(formData)) {
    if (value === '') continue;
    if (value === undefined) continue;
    if (value === null) continue;

    if (key.slice(-6) === '_range') {
      const k = key.slice(0, -6);
      if (value?.[0]) {
        res[`${k}_start`] = value?.[0]?.startOf('d')?.toISOString();
      }
      if (value?.[1]) {
        res[`${k}_end`] = value?.[1]?.endOf('d')?.toISOString();
      }
      continue;
    }

    if (key.slice(-3) === '_in') {
      if (value) {
        res[key] = JSON.stringify([value]);
      }
      continue;
    }
    res[key] = value;
  }
  return res;
};
// 统一修正query参数
export const fixParams = (pageData: any, formData: any) => {
  const page = pageData ? fixPageData(pageData) : {};
  const form = formData ? fixFormData(formData) : {};
  return {
    ...page,
    ...form,
  };
};

export const fixUndefinedKey = (data: any) => {
  let res: any = {};
  for (const [key, value] of Object.entries<any>(data)) {
    console.log(key, value);
    if (value === '') continue;
    if (value === undefined) continue;
    if (value === null) continue;
    res[key] = value;
  }
  return res;
};

// select 的通用搜索函数
export const filterOption = (input: string, option: any) =>
  (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
