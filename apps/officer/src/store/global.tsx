import { createContext, useReducer } from 'react';

type GlobalState = {
  userInfo: API.User | null;
  access: {
    isSuper?: boolean;
    showPage?: {
      [key: string]: boolean;
    };
    page?: {
      [key: string]: boolean;
    };
  };
};
type GlobalActions =
  | {
      type: 'fetchUser';
      payload: API.User;
    }
  | { type: 'logout' };
type GlobalContext = [GlobalState, React.Dispatch<GlobalActions>];

const globalState: GlobalState = {
  userInfo: null,
  access: {},
};
const globalReducer = (state: GlobalState, action: GlobalActions) => {
  switch (action.type) {
    case 'fetchUser':
      const roles = action.payload?.roles || [];
      const isSuper = action.payload?.role === 0;
      function trueRoles(canRoles: string[]) {
        return isSuper || roles?.some((r) => canRoles.includes(r));
      }
      return {
        ...state,
        userInfo: action.payload,
        access: {
          isSuper,
          showPage: {
            // 页面访问权限
            '/pay': true,
            '/student': true,
            '/teacher': trueRoles(['教务', '教务管理员']),
            '/teaching_research': trueRoles(['教务', '教务管理员']),
            '/setting': trueRoles(['教务', '教务管理员']),
            '/pay/review': true,
            '/pay/sub_review': true,
            '/pay/report': action.payload?.role < 2,
            '/student/consult': trueRoles(['咨询', '咨询管理员']),
            '/student/consult/check': trueRoles(['咨询', '咨询管理员']),
            '/student/consult/todo': trueRoles(['咨询', '咨询管理员']),
            '/student/sale': trueRoles(['销售', '销售管理员']),
            '/student/sale/todo': trueRoles(['销售', '销售管理员']),
            '/student/sale/list': trueRoles(['销售', '销售管理员']),
            '/student/graduate': trueRoles(['教务', '教务管理员']),
            '/student/graduate/todo': trueRoles(['教务', '教务管理员']),
            '/student/graduate/list': trueRoles(['教务', '教务管理员']),
            '/student/vip': trueRoles(['教务', '教务管理员']),
            '/student/vip/list': trueRoles(['教务', '教务管理员']),
            '/student/vip/status': trueRoles(['教务', '教务管理员']),
            '/student/class-teacher': trueRoles([
              '学部班主任',
              '大学院班主任',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            '/student/class-teacher/student-list': trueRoles([
              '学部班主任',
              '大学院班主任',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            '/student/class-teacher/student-history': trueRoles([
              '学部班主任',
              '大学院班主任',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            '/teaching_research/todo': trueRoles(['教务', '教务管理员']),
            '/teaching_research/list': trueRoles(['教务', '教务管理员']),
            '/teaching_research/zoom': trueRoles(['教务', '教务管理员']),
            '/teacher/todo': trueRoles(['教务', '教务管理员']),
            '/teacher/list': trueRoles(['教务', '教务管理员']),
          },
          page: {
            // 读取学生字段权限
            'studentInfo.R.full': trueRoles([
              '销售',
              '销售管理员',
              '教务',
              '教务管理员',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            // 教务30天学生字段权限
            'studentInfo.R.un_limit': trueRoles([
              '销售',
              '销售管理员',
              '教务管理员',
              '学部班主任',
              '大学院班主任',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            // 编辑学生字段权限
            'studentInfo.U.full': trueRoles([
              '教务',
              '教务管理员',
            ]),
            // 操作权限
            'studentInfo.UPDATE_COURSE': trueRoles(['教务', '教务管理员']),
            'studentInfo.U': trueRoles([
              '教务',
              '教务管理员',
              '销售',
              '销售管理员',
            ]),
            'studentInfo.U2': trueRoles([
              '教务',
              '教务管理员',
              '咨询',
              '咨询管理员',
            ]),
            'studentInfo.D': trueRoles([
              '教务',
              '教务管理员',
            ]),
            'studentCourses.C': trueRoles([
              '教务',
              '教务管理员',
              '销售',
              '销售管理员',
            ]),
            'studentCourses.U': trueRoles([
              '教务',
              '教务管理员',
              '销售',
              '销售管理员',
            ]),
            'studentCourses.D': trueRoles(['教务', '教务管理员']),
            'studentCourses.JOINT': trueRoles(['教务', '教务管理员']),
            'goalInfo.U': trueRoles([
              '销售',
              '销售管理员',
              '教务',
              '教务管理员',
              '学部班主任',
              '大学院班主任',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            'goalInfo.D': trueRoles([
              '学部班主任',
              '大学院班主任',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            'studentPayment.R': trueRoles([
              '教务',
              '教务管理员',
              '销售',
              '销售管理员',
            ]),
            'studentPayment.C': trueRoles(['教务', '教务管理员']),
            'studentPayment.U': trueRoles(['教务', '教务管理员']),
            'studentPayment.D': trueRoles(['教务管理员']),
            'studentFollow.C': trueRoles([
              '教务',
              '教务管理员',
              '学部班主任',
              '大学院班主任',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            'studentFollow.U': trueRoles([
              '教务',
              '教务管理员',
              '学部班主任',
              '大学院班主任',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            'studentFollow.D': trueRoles([
              '教务管理员',
              '学部班主任管理员',
              '大学院班主任管理员',
            ]),
            'teacher.setting.UPDATE_ROLES': trueRoles(['教务管理员']),
            'teacher.setting.UPDATE_ON_STATUS': trueRoles(['教务管理员']),
            'teacher.setting.UPDATE_BASE_SALARY': trueRoles(['教务管理员']),
            'teacher.list.RESIGN': trueRoles(['教务管理员']),
          },
        },
      };
    case 'logout':
      return globalState;
    default:
      return state;
  }
};

export const GlobalContext = createContext<GlobalContext>([] as any);

const GlobalStore: React.FC<{ children: React.ReactNode }> = (props) => {
  const [state, dispatch] = useReducer(globalReducer, globalState);
  return (
    <GlobalContext.Provider value={[state, dispatch]}>
      {props.children}
    </GlobalContext.Provider>
  );
};

export default GlobalStore;
