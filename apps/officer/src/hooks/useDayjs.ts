import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import dayLocaleData from 'dayjs/plugin/localeData';

export function renderDate(format = 'YYYY-MM-DD') {
  return (date?: Date | string | number) =>
    date ? dayjs(date).format(format) : '';
}

export function renderHours(format = 2) {
  return (v: number) => v?.toFixed(format) + ' 小时';
}

export function renderHoursAndMinutes() {
  return (v: number) => {
    const hours = Math.floor(v);
    const minutes = Math.round((v - hours) * 60);
    return `${hours} 小时 ${minutes} 分钟`;
  };
}

export function initFormDate(params?: Date | string | number) {
  return params ? dayjs(params) : null;
}

export function initDayjs() {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  dayjs.extend(dayLocaleData);
  dayjs.locale('zh-cn');
}

export { dayjs };
