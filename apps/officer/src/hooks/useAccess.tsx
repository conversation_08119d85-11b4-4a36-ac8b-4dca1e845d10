import { useContext, useMemo } from 'react';
////
import { GlobalContext } from '@/store/global';

export const useAccess = () => {
  const [state] = useContext(GlobalContext);
  return state.access;
};

export const renderAccess = (props: AccessProps) => {
  if (props.accessible) return <>{props.children}</>;
  if (props.fallback) return <>{props.fallback}</>;
  return <></>;
};

interface AccessProps {
  accessible?: boolean;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}
export const Access: React.FC<AccessProps> = (props) => {
  if (props.accessible) return <>{props.children}</>;
  if (props.fallback) return <>{props.fallback}</>;
  return <></>;
};
