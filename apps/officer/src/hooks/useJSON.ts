import student<PERSON><PERSON><PERSON> from '@xly/configs/Student/Student.json';
import user<PERSON><PERSON><PERSON> from '@xly/configs/User/User.json';
import salaryJSON from '@xly/configs/Salary/Salary.json';
import timesJSON from '@xly/configs/options/times.json';
import courseJSON from '@xly/configs/course/Course.json';

export interface JSON_OPT {
  value: string | number | boolean;
  label: string;
  [key: string]: any;
}

export function JSONfind(arr: JSON_OPT[]) {
  return (key?: string | number) => arr.find(({ value }) => value === key);
}
export function renderLabel(arr: JSON_OPT[]) {
  return (key?: string | number) =>
    arr.find(({ value }) => value === key)?.label;
}

export { studentJSON, userJSON, salaryJSON, timesJSON, courseJSON };
