import { useMemo } from 'react';
import { useRequest } from 'ahooks';
////
import { getAllUsers } from '@/services/request/user';
import { getAllStudents } from '@/services/request/student';
import { getAllZoomSettings } from '@/services/request/zoomSettings';

interface UseUserOptions {
  params?: Partial<API.User>;
  cacheKey?: string;
}

export const useUserOptions = (opt?: UseUserOptions) => {
  const params = opt?.params || {};
  const { data } = useRequest(
    () => getAllUsers({ page: 0, perPage: 99999, ...params }),
    {
      cacheKey: opt?.cacheKey || 'userOpts',
    },
  );
  const userOptions: { value: string; label: string; [key: string]: any }[] =
    useMemo(() => {
      return (
        data?.users?.map((item: any) => ({
          value: item?._id,
          label: item?.last_name_cn + item?.first_name_cn || '未知',
          ...item,
        })) || []
      );
    }, [data?.totalCount]);

  return {
    userOptions,
    userFind: optFind(userOptions),
    renderUserLabel: renderLabel(userOptions),
  };
};

interface UseStudentOptions {
  params?: Partial<API.Student>;
  cacheKey?: string;
}
export const useStudentOptions = (opt?: UseStudentOptions) => {
  const params = opt?.params || {};
  const { data } = useRequest(
    () => getAllStudents({ page: 0, perPage: 99999, ...params }),
    {
      cacheKey: opt?.cacheKey || 'studentOpts',
    },
  );
  const studentOptions: { value: string; label: string; [key: string]: any }[] =
    useMemo(() => {
      return (
        data?.students?.map((item: any) => ({
          value: item?._id,
          label: item?.name,
          ...item,
        })) || []
      );
    }, [data?.totalCount]);

  return {
    studentOptions,
    studentFind: optFind(studentOptions),
    renderStudentLabel: renderLabel(studentOptions),
  };
};

interface UseZoomOptions {
  params?: Partial<API.Student>;
  cacheKey?: string;
}
export const useZoomOptions = (opt?: UseZoomOptions) => {
  const { data } = useRequest(getAllZoomSettings, {
    cacheKey: opt?.cacheKey || 'zoomOpts',
  });
  const zoomOptions: { value: string; label: string; [key: string]: any }[] =
    useMemo(() => {
      return (
        data?.zoomSettings?.map((item: any) => ({
          value: item?.account,
          label: item?.account,
          ...item,
        })) || []
      );
    }, [data?.totalCount]);

  return { zoomOptions };
};

export function optFind(arr: API.OPTION[]) {
  return (key: string | number) => arr.find(({ value }) => value === key);
}
export function renderLabel(arr: API.OPTION[]) {
  return (key: string | number) =>
    arr.find(({ value }) => value === key)?.label;
}
