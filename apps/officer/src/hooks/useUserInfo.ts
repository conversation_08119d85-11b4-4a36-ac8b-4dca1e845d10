import { useContext } from 'react';
import { useRequest } from 'ahooks';
////
import { GlobalContext } from '@/store/global';
import { getUserById } from '@/services/request/user';
import {
  getUserInfo,
  setUserInfo,
  removeUserInfo,
} from '@/services/useStorage';
import { Modal } from 'antd';

export const useUserInfo = (opt?: any) => {
  const user = getUserInfo();
  const [state, dispatch] = useContext(GlobalContext);
  const userAPI = useRequest(
    async () => await getUserById({ userId: user?._id as string }),
    {
      manual: true,
      ...opt,
      onSuccess: (user) => {
        if (user?.status === 2) {
          Modal.warning({
            title: '警告',
            content: '系统检测到您已离职，请联系教务老师进行复职。',
            centered: true,
            onOk: () => {
              logout();
              window.location.reload();
            },
          });
        }
        updateUserInfo(user);
        opt?.onSuccess?.(user);
      },
    },
  );

  const logout = () => {
    removeUserInfo();
    dispatch({ type: 'logout' });
  };

  const updateUserInfo = (v: any) => {
    setUserInfo(v);
    dispatch({ type: 'fetchUser', payload: v });
  };

  return {
    userInfo: state.userInfo || user || userAPI.data,
    logout,
    updateUserInfo,
    userAPI,
  };
};
