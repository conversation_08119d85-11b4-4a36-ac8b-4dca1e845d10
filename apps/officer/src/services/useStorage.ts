import { LOCAL_STORAGE_KEY } from '@/utils/const';

const loginKey = LOCAL_STORAGE_KEY + '.userLogin';

/**
 *
 * 通过param进行登陆判定
 * @param {*} userLogin
 * @returns isLogin: boolean
 */
export function checkUserLogin() {
  let isLogin = false;
  const userLogin = getUserInfo();
  try {
    const { _id, expiryDate } = userLogin;
    isLogin = !!_id && _id !== 'null' && !!expiryDate;
    if (!isLogin) {
      console.log('请登录');
    } else if (expiryDate && new Date(expiryDate) <= new Date()) {
      console.log('token已过期');
      isLogin = false;
    }
  } catch (err) {
    console.log(err);
  }
  return isLogin;
}

export function setUserInfo(data: API.User) {
  const remainingMilliseconds = 24 * 60 * 60 * 1000;
  const expiryDate = new Date(new Date().getTime() + remainingMilliseconds);
  localStorage.setItem(loginKey, JSON.stringify({ ...data, expiryDate }));
}

export function removeUserInfo() {
  localStorage.removeItem(loginKey);
}

export function getUserInfo(): API.User {
  return JSON.parse(localStorage.getItem(loginKey) || '{}');
}
