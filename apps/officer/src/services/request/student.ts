import API from './Api';

// 获取所有学生 GET /api/students
interface GetAllStudents extends API.PagiNation<API.Student> {}
export async function getAllStudents(params?: GetAllStudents) {
  return API.get('/students', {
    params,
  });
}

// 获取单个学生 GET /api/students
interface GetStudentById {
  studentId: API.ID;
}
export async function getStudentById(params?: GetStudentById) {
  return API.get('/students/' + params?.studentId);
}

// 创建学生 POST /api/students
interface CreateStudent extends API.Student {}
export async function createStudent(params: CreateStudent) {
  return API.post('/students', {
    data: {
      ...params,
    },
  });
}

// 更新学生 PATCH /api/students/:id
interface UpdateStudent extends Partial<API.Student> {
  studentId: API.ID;
}
export async function updateStudent(params: UpdateStudent) {
  const { studentId, ...data } = params;
  return API.patch('/students/' + studentId, {
    data: {
      ...data,
    },
  });
}

// 删除学生 DELETE /api/students/:id
interface DeleteStudentById {
  studentId: API.ID;
}
export async function deleteStudentById(params: DeleteStudentById) {
  return API.delete('/students/' + params.studentId);
}

// 新建课程 POST /api/students/add-course/:id
interface AddCourseToStudent extends Partial<API.Course> {
  studentId: API.ID;
}
export async function addCourseToStudent(params: AddCourseToStudent) {
  const { studentId, ...data } = params;
  return API.post('/students/add-course/' + studentId, {
    data: {
      ...data,
    },
  });
}

// 更新课程 POST /api/students/update-course/:id
interface UpdateCourseToStudent {
  studentId: API.ID;
  courseId: API.ID;
  course: Partial<API.Course>;
}
export async function updateCourseToStudent(params: UpdateCourseToStudent) {
  return API.post('/students/update-course/', {
    data: {
      student_id: params.studentId,
      course_id: params.courseId,
      course: params.course,
    },
  });
}

// 删除课程 POST /api/students/delete-course
interface RemoveCourseFromStudent {
  studentId: API.ID;
  courseId: API.ID;
}
export async function removeCourseFromStudent(params: RemoveCourseFromStudent) {
  return API.post('/students/delete-course', {
    data: {
      student_id: params.studentId,
      course_id: params.courseId,
    },
  });
}

// 新建付款记录 POST /api/students/add-payment/:id
interface AddPaymentHistoryToStudent extends Partial<API.PaymentHistory> {
  studentId: API.ID;
}
export async function addPaymentHistoryToStudent(
  params: AddPaymentHistoryToStudent,
) {
  const { studentId, ...data } = params;
  return API.post('/students/add-payment/' + studentId, {
    data: {
      ...data,
    },
  });
}

// 更新付款记录 POST /api/students/update-payment/:id
interface UpdatePaymentHistoryToStudent {
  studentId: API.ID;
  paymentHistoryId: API.ID;
  paymentHistory: Partial<API.PaymentHistory>;
}
export async function updatePaymentHistoryToStudent(
  params: UpdatePaymentHistoryToStudent,
) {
  return API.post('/students/update-payment/', {
    data: {
      student_id: params.studentId,
      payment_id: params.paymentHistoryId,
      payment: params.paymentHistory,
    },
  });
}

// 删除付款记录 POST /api/students/delete-payment
interface RemovePaymentHistoryFromStudent {
  studentId: API.ID;
  paymentHistoryId: API.ID;
}
export async function removePaymentHistoryFromStudent(
  params: RemovePaymentHistoryFromStudent,
) {
  return API.post('/students/delete-payment', {
    data: {
      student_id: params.studentId,
      payment_id: params.paymentHistoryId,
    },
  });
}

// 新建课程 POST /api/students/add-follow-history/:id
interface AddFollowHistoryToStudent extends Partial<API.FollowHistory> {
  studentId: API.ID;
}
export async function addFollowHistoryToStudent(
  params: AddFollowHistoryToStudent,
) {
  const { studentId, ...data } = params;
  return API.post('/students/add-follow-history/' + studentId, {
    data: {
      ...data,
    },
  });
}

// 更新课程 POST /api/students/update-follow-history/:id
interface UpdateFollowHistoryToStudent {
  studentId: API.ID;
  followHistoryId: API.ID;
  followHistory: Partial<API.FollowHistory>;
}
export async function updateFollowHistoryToStudent(
  params: UpdateFollowHistoryToStudent,
) {
  return API.post('/students/update-follow-history/', {
    data: {
      student_id: params.studentId,
      follow_history_id: params.followHistoryId,
      follow_history: params.followHistory,
    },
  });
}

// 删除课程 POST /api/students/delete-follow-history
interface RemoveFollowHistoryFromStudent {
  studentId: API.ID;
  followHistoryId: API.ID;
}
export async function removeFollowHistoryFromStudent(
  params: RemoveFollowHistoryFromStudent,
) {
  return API.post('/students/delete-follow-history', {
    data: {
      student_id: params.studentId,
      follow_history_id: params.followHistoryId,
    },
  });
}

// 生成合同编号 POST /api/students/gen-contract-no/:id
interface GenContractNo {
  studentId: API.ID;
}
export async function genContractNo(params: GenContractNo) {
  return API.post('/students/gen-contract-no/' + params.studentId);
}

// 新建升学信息 POST /api/students/add-goal/:id
interface AddGoalToStudent extends Partial<API.GoalInfo> {
  studentId: API.ID;
}
export async function addGoalToStudent(
  params: AddGoalToStudent,
) {
  const { studentId, ...data } = params;
  return API.post('/students/add-goal/' + studentId, {
    data: {
      ...data,
    },
  });
}

// 更新升学信息 POST /api/students/update-goal
interface UpdateGoalFromStudent {
  studentId: API.ID;
  goalId: API.ID;
  goal: Partial<API.GoalInfo>;
}
export async function updateGoalFromStudent(
  params: UpdateGoalFromStudent,
) {
  return API.post('/students/update-goal/', {
    data: {
      student_id: params.studentId,
      goal_id: params.goalId,
      goal: params.goal,
    },
  });
}

// 删除升学信息 POST /api/students/delete-goal
interface RemoveGoalFromStudent {
  studentId: API.ID;
  GoalId: API.ID;
}
export async function removeGoalFromStudent(
  params: RemoveGoalFromStudent,
) {
  return API.post('/students/delete-goal', {
    data: {
      student_id: params.studentId,
      goal_id: params.GoalId,
    },
  });
}

// 检查微信号是否已存在 GET /api/students/check-wechat
interface CheckWechatExists {
  wechat: string;
}
export async function checkWechatExists(params: CheckWechatExists) {
  return API.get('/students/check-wechat', {
    params: {
      wechat: params.wechat,
    },
  });
}