import API from './Api';

// 获取所有用户 GET /api/users
interface GetAllUsers extends Partial<API.User> {
  page: number;
  perPage: number;
}
export async function getAllUsers(params?: GetAllUsers) {
  return API.get('/users', {
    params,
  });
}

// 获取单个用户 GET /api/users
interface GetUserById {
  userId: API.ID;
}
export async function getUserById(params?: GetUserById) {
  return API.get('/users/' + params?.userId);
}

// 创建用户 POST /api/users
interface CreateUser extends API.User {}
export async function createUser(params: CreateUser) {
  return API.post('/users', {
    data: {
      ...params,
    },
  });
}

// 更新用户 PATCH /api/users/:id
interface UpdateUser extends Partial<API.User> {
  userId: API.ID;
}
export async function updateUser(params: UpdateUser) {
  return API.patch('/users/' + params.userId, {
    data: {
      ...params,
    },
  });
}

// 删除用户 DELETE /api/users/:id
interface DeleteUserById {
  userId: API.ID;
}
export async function deleteUserById(params: DeleteUserById) {
  return API.delete('/users/' + params.userId);
}

// 用户登陆 POST /api/users/login
interface UserLogin {
  userId: API.ID;
}
export async function userLogin(params: UserLogin) {
  return API.post('/users/login', {
    data: {
      ...params,
    },
  });
}

// 用户设置密码 POST /api/users/reset-password
interface ResetUserPassword {
  email: string;
  oldPassword: string;
  newPassword: string;
}
export async function resetUserPassword(params: ResetUserPassword) {
  return API.post('/users/reset-password', {
    data: {
      ...params,
    },
  });
}
