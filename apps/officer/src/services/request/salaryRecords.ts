import API from './Api';

// 获取所有用户申报 GET /api/salary-records
interface GetAllSalaryRecords extends API.PagiNation<API.SalaryRecord> {
  apply_date_start?: string;
  apply_date_end?: string;
  apply_status_in?: string;
  work_date_start?: string;
  work_date_end?: string;
  check_date_start?: string;
  check_date_end?: string;
}
export async function getAllSalaryRecords(params?: GetAllSalaryRecords) {
  return API.get('/salary-records', {
    params,
  });
}

// 获取单个用户申报 GET /api/salary-records
interface GetSalaryRecordById {
  salaryRecordId: API.ID;
}
export async function getSalaryRecordById(params?: GetSalaryRecordById) {
  return API.get('/salary-records/' + params?.salaryRecordId);
}

// 获取审核月度列表 GET /api/salary-records/check-month-stat/:id
interface CheckMonthStat {
  userId?: API.ID;
  isSub: boolean;
}
export async function checkMonthStat(params?: CheckMonthStat) {
  return API.post('/salary-records/check-month-stat/' + params?.userId, {
    data: {
      ...params,
    },
  });
}

// 创建用户申报 POST /api/salary-records
interface CreateSalaryRecord extends Partial<API.SalaryRecord> {}
export async function createSalaryRecord(params: CreateSalaryRecord) {
  return API.post('/salary-records', {
    data: {
      ...params,
    },
  });
}

// 更新用户申报 PATCH /api/salary-records/:id
interface UpdateSalaryRecord extends Partial<API.SalaryRecord> {
  salaryRecordId: API.ID;
}
export async function updateSalaryRecord(params: UpdateSalaryRecord) {
  return API.patch('/salary-records/' + params.salaryRecordId, {
    data: {
      ...params,
    },
  });
}

// 删除用户申报 DELETE /api/salary-records/:id
interface DeleteSalaryRecordById {
  salaryRecordId: API.ID;
}
export async function deleteSalaryRecordById(params: DeleteSalaryRecordById) {
  return API.delete('/salary-records/' + params.salaryRecordId);
}

// 提交用户申报 POST /api/salary-records/apply/:id
interface ApplySalaryRecords {
  userId?: API.ID;
}
export async function applySalaryRecords(params: ApplySalaryRecords) {
  return API.post('/salary-records/apply/' + params.userId);
}

// 提交用户审核 POST /api/salary-records/check/:id
interface CheckSalaryRecords {
  check_userId?: API.ID;
}
export async function checkSalaryRecords(params: CheckSalaryRecords) {
  return API.post('/salary-records/check/' + params.check_userId);
}
