import API from './Api';

// 获取所有用户申报统计 GET /api/salary-months
interface GetAllSalaryMonths {
  check_user?: string;
  year: string;
  month: string;
}
export async function getAllSalaryMonths(params?: GetAllSalaryMonths) {
  return API.get('/salary-months', {
    params,
  });
}

// 更新用户申报统计 POST /api/salary-months/summary
interface SummarySalaryMonth extends Partial<API.SalaryMonth> {
  check_user: API.ID;
  check_date_start: string;
  check_date_end: string;
}
export async function summarySalaryMonth(params: SummarySalaryMonth) {
  return API.post('/salary-months/summary', {
    data: {
      ...params,
    },
  });
}
