import API from './Api';

// 获取所有VIP GET /api/vip-courses
interface GetAllVipCourses extends API.PagiNation<API.VipCourse> { }
export async function getAllVipCourses(params?: GetAllVipCourses) {
  return API.get('/vip-courses', {
    params,
  });
}

// 获取单个VIP课程 GET /api/vip-courses
interface GetVipCourseById {
  vipCourseId: API.ID;
}
export async function getVipCourseById(params?: GetVipCourseById) {
  return API.get('/vip-courses/' + params?.vipCourseId);
}

// 创建VIP课程 POST /api/vip-courses
interface CreateVipCourse extends API.VipCourse { }
export async function createVipCourse(params: CreateVipCourse) {
  return API.post('/vip-courses', {
    data: {
      ...params,
    },
  });
}

// 更新VIP课程 PATCH /api/vip-courses/:id
interface UpdateVipCourse extends Partial<API.VipCourse> {
  vipCourseId: API.ID;
}
export async function updateVipCourse(params: UpdateVipCourse) {
  const { vipCourseId, ...data } = params;
  return API.patch('/vip-courses/' + vipCourseId, {
    data: {
      ...data,
    },
  });
}

// 删除VIP课程 DELETE /api/vip-courses/:id
interface DeleteVipCourseById {
  vipCourseId: API.ID;
}
export async function deleteVipCourseById(params: DeleteVipCourseById) {
  return API.delete('/vip-courses/' + params.vipCourseId);
}

// 自动申报VIP工资 DELETE /api/vip-courses/finish
interface FinishVipCourse {
  ids: API.ID[];
}
export async function finishVipCourse(params: FinishVipCourse) {
  return API.post('/vip-courses/finish', {
    data: {
      ...params
    }
  });
}
