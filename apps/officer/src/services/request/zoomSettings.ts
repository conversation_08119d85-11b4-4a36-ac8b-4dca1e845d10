import API from './Api';

// 获取所有zoom账户 GET /api/zoom-settings
interface GetAllZoomSettings extends Partial<API.ZoomSetting> {
  page: number;
  perPage: number;
}
export async function getAllZoomSettings(params?: GetAllZoomSettings) {
  return API.get('/zoom-settings', {
    params,
  });
}

// 获取单个zoom账户 GET /api/zoom-settings
interface GetZoomById {
  zoomId: API.ID;
}
export async function getZoomById(params?: GetZoomById) {
  return API.get('/zoom-settings/' + params?.zoomId);
}

// 创建zoom账户 POST /api/zoom-settings
interface CreateZoomSetting extends API.ZoomSetting {}
export async function createZoomSetting(params: CreateZoomSetting) {
  return API.post('/zoom-settings', {
    data: {
      ...params,
    },
  });
}

// 更新zoom账户 PATCH /api/zoom-settings/:id
interface UpdateZoomSetting extends Partial<API.ZoomSetting> {
  zoomId: API.ID;
}
export async function updateZoomSetting(params: UpdateZoomSetting) {
  return API.patch('/zoom-settings/' + params.zoomId, {
    data: {
      ...params,
    },
  });
}

// 删除zoom账户 DELETE /api/zoom-settings/:id
interface DeleteZoomSettingById {
  zoomId: API.ID;
}
export async function deleteZoomSettingById(params: DeleteZoomSettingById) {
  return API.delete('/zoom-settings/' + params.zoomId);
}
