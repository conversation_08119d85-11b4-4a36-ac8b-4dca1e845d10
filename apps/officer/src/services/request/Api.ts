import queryString from 'query-string';
const { API_URL } = process.env;

// implement a method to execute all the request from here.
const apiRequest = async (method: any, url: string, request: any) => {
  const query = request?.params || {};
  const qs =
    Object.keys(query).length === 0 ? '' : '?' + queryString.stringify(query);
  const res = await fetch(API_URL + url + qs, {
    method: method,
    headers: {
      'Content-Type': 'application/json',
    },
    ...request,
    body: JSON.stringify(request?.data),
  });
  const data = await res.json();
  if (!!data.error) {
    throw data;
  }
  return data;
};

// function to execute the http get request
const get = (url: string, request?: any) => apiRequest('GET', url, request);

// function to execute the http delete request
const deleteRequest = (url: string, request?: any) =>
  apiRequest('DELETE', url, request);

// function to execute the http post request
const post = (url: string, request?: any) => apiRequest('POST', url, request);

// function to execute the http put request
const put = (url: string, request?: any) => apiRequest('PUT', url, request);

// function to execute the http path request
const patch = (url: string, request?: any) => apiRequest('PATCH', url, request);

// expose your method to other services or actions
const API = {
  get,
  delete: deleteRequest,
  post,
  put,
  patch,
};
export default API;
