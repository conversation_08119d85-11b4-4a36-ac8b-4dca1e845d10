import AWS from 'aws-sdk';
import JS<PERSON><PERSON> from 'jszip';

const S3 = process.env?.S3 as any;
const BUCKET = S3?.S3_BUCKET;
const REGION = S3?.REGION;
const IDENTITY_POOL_ID = S3?.IDENTITY_POOL_ID;
export const BASIC_PATH = S3?.BASIC_PATH;

if (IDENTITY_POOL_ID && REGION) {
  AWS.config.region = REGION;
  AWS.config.credentials = new AWS.CognitoIdentityCredentials({
    IdentityPoolId: IDENTITY_POOL_ID,
  });
}

const s3 = new AWS.S3();

export const upload_image = async (file: any) => {
  try {
    const params = {
      Bucket: BUCKET,
      Key: file.name,
      ContentType: file.type,
      Body: file,
    };
    const res = await s3.putObject(params).promise();
    if (res.$response?.error) {
      throw res.$response?.error;
    }
    return {
      name: file.name,
      url: BASIC_PATH + file.name,
    };
  } catch (error) {
    throw error;
  }
};

export const downloadFiles = async (Prefix: string) => {
  s3.listObjectsV2(
    {
      Bucket: BUCKET,
      Prefix,
    },
    async (err, data) => {
      if (err) {
        console.log(err, err.stack); // an error occurred
      } else {
        try {
          const zip = new JSZip();
          await Promise.all(
            (data.Contents || [])?.map(async (item) => {
              if (item.Key) {
                try {
                  let keys = item.Key.split('/');
                  const filename = keys[3] || 'file_error';
                  const tempPath = keys[2] || 'path_error';
                  const folder = zip.folder(tempPath);
                  const res = await fetch(BASIC_PATH + item.Key);
                  const blob = await res.blob();
                  folder?.file(filename, blob);
                } catch (error) {
                  console.log(error);
                }
              }
            }),
          );
          const content = await zip.generateAsync({ type: 'blob' });
          const link = document.createElement('a');
          link.download = (Prefix?.split('/')?.[1] || '') + '备课文件';
          link.href = URL.createObjectURL(content);
          link.click();
          URL.revokeObjectURL(link.href);
        } catch (error) {
          console.log(error);
        }
      }
    },
  );
};
