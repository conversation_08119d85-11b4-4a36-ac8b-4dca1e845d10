name: user.xly-edu.com
on:
  push:
    branches: [prod]
    paths:
      - 'apps/user/**'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Use Node.js
        uses: actions/checkout@v3
      - name: Clean packages
        run: yarn cache clean
      - name: Install packages
        run: yarn
      - name: Run yarn build
        run: yarn workspace @xly/user build
      - name: Upload build result
        uses: actions/upload-artifact@v3
        with:
          name: build
          path: dist/

  deploy:
    needs: [build]
    runs-on: ubuntu-latest
    steps:
      - name: Download build result
        uses: actions/download-artifact@v3
        with:
          name: build
          path: dist/
      - name: Publish to AWS S3
        uses: opspresso/action-s3-sync@master
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: 'ap-northeast-1'
          FROM_PATH: 'dist/xly-user'
          DEST_PATH: 's3://user.xly-edu.com'
          OPTIONS: '--delete'
