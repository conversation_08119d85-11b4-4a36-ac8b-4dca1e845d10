#!/usr/bin/env node

import inquirer from 'inquirer';
import { spawn } from 'child_process';

const prompts = [
  {
    name: 'packages',
    message: '选择要执行的项目:',
    type: 'list',
    pageSize: 11,
    choices: [
      { name: '管理側(officer)', value: '@xly/officer' },
      { name: '利用側(user)', value: '@xly/user' },
    ],
  },
];

const [, , ...commend] = process.argv;

inquirer
  .prompt(prompts)
  .then((answers) => {
    spawn('yarn', ['workspace', answers.packages, ...commend], {
      stdio: 'inherit',
    });
  })
  .catch((error) => console.log(error));
